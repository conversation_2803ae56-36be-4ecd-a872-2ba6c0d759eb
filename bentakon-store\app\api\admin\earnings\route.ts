import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { rateLimit } from '../../../lib/security'
import { calculateTotalEarnings, calculateEarningsByPeriod, getTopProfitableProducts, getEarningsByCurrency } from '../../../utils/earnings'

// GET /api/admin/earnings - Get earnings analytics
export async function GET(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 50)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const url = new URL(request.url)
    const period = parseInt(url.searchParams.get('period') || '30') // days
    const limit = parseInt(url.searchParams.get('limit') || '5') // top products limit

    // Get orders with product and package data
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        amount,
        status,
        product_id,
        package_id,
        created_at,
        products (
          id,
          title,
          original_price,
          user_price
        ),
        packages (
          id,
          name,
          original_price,
          user_price
        )
      `)
      .eq('tenant_id', profile.tenant_id)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })

    if (ordersError) {
      console.error('Earnings API - Orders error:', ordersError)
      return NextResponse.json({ error: 'Failed to fetch orders' }, { status: 500 })
    }

    // Get products data
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        id,
        title,
        original_price,
        user_price,
        packages (
          id,
          name,
          original_price,
          user_price
        )
      `)
      .eq('tenant_id', profile.tenant_id)

    if (productsError) {
      console.error('Earnings API - Products error:', productsError)
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 })
    }

    // Flatten packages for calculations
    const allPackages = products.flatMap(p => p.packages || [])

    // Calculate earnings metrics
    const totalEarnings = calculateTotalEarnings(orders || [], products || [], allPackages)
    const periodEarnings = calculateEarningsByPeriod(orders || [], products || [], allPackages, period)
    const topProducts = getTopProfitableProducts(orders || [], products || [], allPackages, limit)
    const currencyBreakdown = getEarningsByCurrency(orders || [], products || [], allPackages)

    // Calculate additional metrics
    const totalCost = totalEarnings.totalCost
    const averageProfitPerOrder = totalEarnings.orderCount > 0 
      ? totalEarnings.totalProfit / totalEarnings.orderCount 
      : 0

    // Calculate period comparison (previous period)
    const previousPeriodEarnings = calculateEarningsByPeriod(
      orders || [],
      products || [],
      allPackages,
      period,
      period // offset by period days
    )

    const profitGrowth = previousPeriodEarnings.totalProfit > 0
      ? ((periodEarnings.totalProfit - previousPeriodEarnings.totalProfit) / previousPeriodEarnings.totalProfit) * 100
      : 0

    return NextResponse.json({
      success: true,
      data: {
        // Overall metrics
        totalRevenue: totalEarnings.totalRevenue,
        totalCost: totalCost,
        totalProfit: totalEarnings.totalProfit,
        profitMargin: totalEarnings.profitMargin,
        orderCount: totalEarnings.orderCount,
        averageOrderValue: totalEarnings.averageOrderValue,
        averageProfitPerOrder,

        // Period metrics
        period: {
          days: period,
          revenue: periodEarnings.totalRevenue,
          cost: periodEarnings.totalCost,
          profit: periodEarnings.totalProfit,
          profitMargin: periodEarnings.profitMargin,
          orderCount: periodEarnings.orderCount,
          profitGrowth
        },

        // Top performing products
        topProducts: topProducts.map(product => ({
          productId: product.productId,
          productName: product.productName,
          orderCount: product.orderCount,
          totalRevenue: product.totalRevenue,
          totalCost: product.totalCost,
          totalProfit: product.totalProfit,
          profitMargin: product.profitMargin
        })),

        // Currency breakdown
        currencyBreakdown: currencyBreakdown.map(currency => ({
          currency: currency.currency,
          orderCount: currency.orderCount,
          totalRevenueUSD: currency.totalRevenueUSD,
          totalCostUSD: currency.totalCostUSD,
          totalProfitUSD: currency.totalProfitUSD,
          profitMarginPercent: currency.profitMarginPercent,
          originalCurrencyAmount: currency.originalCurrencyAmount
        }))
      }
    })

  } catch (error) {
    console.error('Error in GET /api/admin/earnings:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}


