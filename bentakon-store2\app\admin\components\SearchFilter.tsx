"use client"

import { Search, Filter, X } from "lucide-react"
import { useState, useEffect, useMemo } from "react"

interface FilterOption {
  value: string
  label: string
  count?: number
}

interface SearchFilterProps {
  searchValue: string
  onSearchChange: (value: string) => void
  placeholder?: string
  filters?: Array<{
    key: string
    label: string
    options: FilterOption[]
  }>
  activeFilters?: Record<string, string>
  onFilterChange?: (filterKey: string, value: string) => void
  onClearFilters?: () => void
  showFilterCount?: boolean
}

export default function SearchFilter({
  searchValue,
  onSearchChange,
  placeholder = "البحث...",
  filters = [],
  activeFilters = {},
  onFilterChange,
  onClearFilters,
  showFilterCount = true
}: SearchFilterProps) {
  const [showFilters, setShowFilters] = useState(false)
  const [localSearchValue, setLocalSearchValue] = useState(searchValue)

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearchChange(localSearchValue)
    }, 300)

    return () => clearTimeout(timer)
  }, [localSearchValue, onSearchChange])

  // Update local value when external value changes
  useEffect(() => {
    setLocalSearchValue(searchValue)
  }, [searchValue])

  const activeFilterCount = useMemo(() =>
    Object.values(activeFilters).filter(Boolean).length,
    [activeFilters]
  )
  const hasActiveFilters = activeFilterCount > 0

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={localSearchValue}
          onChange={(e) => setLocalSearchValue(e.target.value)}
          placeholder={placeholder}
          className="w-full pr-12 pl-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
        />
        {localSearchValue && (
          <button
            onClick={() => {
              setLocalSearchValue("")
              onSearchChange("")
            }}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Filter Controls */}
      {filters.length > 0 && (
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-xl border transition-all duration-200 ${
              showFilters || hasActiveFilters
                ? "bg-purple-600/20 border-purple-500/50 text-purple-400"
                : "bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500/50"
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>الفلاتر</span>
            {showFilterCount && hasActiveFilters && (
              <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                {activeFilterCount}
              </span>
            )}
          </button>

          {hasActiveFilters && onClearFilters && (
            <button
              onClick={onClearFilters}
              className="text-gray-400 hover:text-red-400 text-sm transition-colors"
            >
              مسح جميع الفلاتر
            </button>
          )}
        </div>
      )}

      {/* Filter Options */}
      {showFilters && filters.length > 0 && (
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 border border-gray-700/50 space-y-4">
          {filters.map((filter) => (
            <div key={filter.key} className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                {filter.label}
              </label>
              <select
                value={activeFilters[filter.key] || ""}
                onChange={(e) => onFilterChange?.(filter.key, e.target.value)}
                className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
              >
                <option value="">جميع {filter.label}</option>
                {filter.options.map((option) => (
                  <option key={option.value} value={option.value} className="bg-gray-800">
                    {option.label}
                    {option.count !== undefined && ` (${option.count})`}
                  </option>
                ))}
              </select>
            </div>
          ))}
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value) return null
            
            const filter = filters.find(f => f.key === key)
            const option = filter?.options.find(o => o.value === value)
            
            if (!filter || !option) return null

            return (
              <div
                key={key}
                className="flex items-center space-x-2 space-x-reverse px-3 py-1 bg-purple-600/20 border border-purple-500/50 rounded-lg text-purple-400 text-sm"
              >
                <span>{filter.label}: {option.label}</span>
                <button
                  onClick={() => onFilterChange?.(key, "")}
                  className="text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
