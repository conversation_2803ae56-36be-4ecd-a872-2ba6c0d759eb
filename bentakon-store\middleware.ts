import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { TenantResolver } from './app/lib/tenant'
import { addSecurityHeaders } from './app/lib/security-headers'

export async function middleware(request: NextRequest) {
  // Skip middleware for static files and API routes that don't need tenant context
  if (
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.startsWith('/api/auth') ||
    request.nextUrl.pathname.includes('.') // Static files
  ) {
    return NextResponse.next()
  }

  try {
    // Resolve tenant from request
    const tenant = await TenantResolver.resolveTenantFromRequest(request)
    
    if (!tenant) {
      // If no tenant found and not in development, show tenant not found page
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.redirect(new URL('/tenant-not-found', request.url))
      }
      
      // In development, try to get default tenant
      const defaultTenant = await TenantResolver.resolveTenantFromEnvironment()
      if (!defaultTenant) {
        console.warn('No tenant found and no default tenant available')
        return NextResponse.next()
      }
      
      // Set default tenant in headers for development
      const response = NextResponse.next()
      response.headers.set('x-tenant-id', defaultTenant.id)
      response.headers.set('x-tenant-slug', defaultTenant.slug)
      response.headers.set('x-tenant-name', encodeURIComponent(defaultTenant.name))
      response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(defaultTenant.theme_config)))
      return response
    }

    // Check if tenant is active
    if (tenant.status !== 'active') {
      if (tenant.status === 'suspended') {
        return NextResponse.redirect(new URL('/tenant-suspended', request.url))
      }
      if (tenant.status === 'inactive') {
        return NextResponse.redirect(new URL('/tenant-inactive', request.url))
      }
    }

    // Add tenant information to request headers (encode non-ASCII characters)
    const response = NextResponse.next()

    // Add security headers
    addSecurityHeaders(response)

    response.headers.set('x-tenant-id', tenant.id)
    response.headers.set('x-tenant-slug', tenant.slug)
    response.headers.set('x-tenant-name', encodeURIComponent(tenant.name))
    response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(tenant.theme_config)))
    response.headers.set('x-tenant-settings', encodeURIComponent(JSON.stringify(tenant.settings)))

    // Add tenant info to cookies with security settings
    response.cookies.set('tenant-id', tenant.id, {
      httpOnly: true, // Security: Prevent XSS access to tenant ID
      secure: process.env.NODE_ENV === 'production', // Only secure in production
      sameSite: 'lax', // Less strict for development
      maxAge: 60 * 60 * 24, // 24 hours
      path: '/' // Explicit path for security
    })

    // Add a separate non-httpOnly cookie for client-side tenant name only
    response.cookies.set('tenant-name', encodeURIComponent(tenant.name), {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production', // Only secure in production
      sameSite: 'lax', // Less strict for development
      maxAge: 60 * 60 * 24,
      path: '/'
    })

    response.cookies.set('tenant-slug', tenant.slug, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    })

    return response
  } catch (error) {
    console.error('Middleware error:', error)
    
    // In case of error, try to continue with default tenant
    try {
      const defaultTenant = await TenantResolver.getDefaultTenant()
      if (defaultTenant) {
        const response = NextResponse.next()
        response.headers.set('x-tenant-id', defaultTenant.id)
        response.headers.set('x-tenant-slug', defaultTenant.slug)
        response.headers.set('x-tenant-name', encodeURIComponent(defaultTenant.name))
        response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(defaultTenant.theme_config)))
        return response
      }
    } catch (fallbackError) {
      console.error('Fallback tenant resolution failed:', fallbackError)
    }
    
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (authentication endpoints)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
