"use client"

import { useState } from "react"
import { useTenant } from "../contexts/TenantContext"

export default function TenantSwitcher() {
  const { tenant } = useTenant()
  const [isOpen, setIsOpen] = useState(false)

  // Only show this component in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const tenantOptions = [
    { slug: 'main', name: 'بنتاكون الرئيسي (ألعاب)', url: 'localhost:3001' },
    { slug: 'store2', name: 'متجر الإلكترونيات الذكية', url: 'store2.localhost:3001' },
    { slug: 'store3', name: 'متجر الأزياء العصرية', url: 'store3.localhost:3001' },
  ]

  const createTenantUrl = (slug: string) => {
    if (slug === 'main') {
      return 'http://localhost:3001'
    }
    return `http://${slug}.localhost:3001`
  }

  return (
    <div className="fixed bottom-4 left-4 bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-50">
      <h3 className="text-white font-bold mb-2">🏪 تبديل المتاجر</h3>
      
      <div className="space-y-2">
        <p className="text-gray-300 text-sm">
          المتجر الحالي: <span className="text-purple-400 font-medium">{tenant?.name || 'غير محدد'}</span>
        </p>
        <p className="text-gray-400 text-xs">
          المعرف: {tenant?.slug || 'غير محدد'}
        </p>
        
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
        >
          {isOpen ? "إخفاء المتاجر" : "عرض المتاجر"}
        </button>
        
        {isOpen && (
          <div className="space-y-1 mt-2">
            {tenantOptions.map((option) => (
              <a
                key={option.slug}
                href={createTenantUrl(option.slug)}
                className={`block w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                  tenant?.slug === option.slug
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                }`}
              >
                <div className="font-medium">{option.name}</div>
                <div className="text-xs opacity-75">{option.url}</div>
              </a>
            ))}
          </div>
        )}
        
        <div className="mt-3 p-2 bg-gray-700 rounded text-xs text-gray-300">
          💡 <strong>نصيحة:</strong> لإنشاء متجر جديد، أضف subdomain مثل:
          <br />
          <code className="text-purple-400">newstore.localhost:3001</code>
        </div>
      </div>
    </div>
  )
}
