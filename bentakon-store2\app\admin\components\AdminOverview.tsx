"use client"

import { Clock, CheckCircle, XCircle, TrendingUp } from "lucide-react"
import { convertAndFormatPrice } from "../../utils/currency"
import AdminStats from "./AdminStats"

interface AdminOverviewProps {
  stats: {
    totalProducts: number
    totalUsers: number
    totalOrders: number
    totalRevenue: number
    pendingOrders: number
    completedOrders: number
  }
  recentOrders: Array<{
    id: string
    userId: string
    amount: number
    status: string
    createdAt: string
  }>
}

export default function AdminOverview({ stats, recentOrders }: AdminOverviewProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-400" />
      case "failed":
        return <XCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "مكتمل"
      case "pending":
        return "قيد الانتظار"
      case "failed":
        return "فاشل"
      default:
        return "غير محدد"
    }
  }

  return (
    <div className="space-y-8">
      {/* Stats Cards */}
      <AdminStats stats={stats} />

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <Clock className="w-6 h-6 text-yellow-400" />
            <h3 className="text-lg font-semibold">الطلبات المعلقة</h3>
          </div>
          <p className="text-3xl font-bold text-yellow-400">{stats.pendingOrders}</p>
          <p className="text-gray-400 text-sm mt-2">تحتاج إلى مراجعة</p>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <CheckCircle className="w-6 h-6 text-green-400" />
            <h3 className="text-lg font-semibold">الطلبات المكتملة</h3>
          </div>
          <p className="text-3xl font-bold text-green-400">{stats.completedOrders}</p>
          <p className="text-gray-400 text-sm mt-2">تم تنفيذها بنجاح</p>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <TrendingUp className="w-6 h-6 text-purple-400" />
            <h3 className="text-lg font-semibold">معدل النجاح</h3>
          </div>
          <p className="text-3xl font-bold text-purple-400">
            {stats.totalOrders > 0 ? Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0}%
          </p>
          <p className="text-gray-400 text-sm mt-2">من إجمالي الطلبات</p>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        <div className="p-6 border-b border-gray-700/50">
          <h3 className="text-xl font-semibold">الطلبات الأخيرة</h3>
        </div>
        <div className="p-6">
          {recentOrders.length === 0 ? (
            <p className="text-gray-400 text-center py-8">لا توجد طلبات حديثة</p>
          ) : (
            <div className="space-y-4">
              {recentOrders.slice(0, 5).map((order) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between p-4 bg-gray-700/30 rounded-xl border border-gray-600/30"
                >
                  <div className="flex items-center space-x-4 space-x-reverse">
                    {getStatusIcon(order.status)}
                    <div>
                      <p className="font-medium">طلب #{order.id}</p>
                      <p className="text-sm text-gray-400">
                        {new Date(order.createdAt).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold">{convertAndFormatPrice(order.amount)}</p>
                    <p className="text-sm text-gray-400">{getStatusText(order.status)}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
