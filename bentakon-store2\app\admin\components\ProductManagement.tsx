"use client"

import { useState } from "react"
import { <PERSON>, Chevron<PERSON>eft, ChevronRight, Check } from "lucide-react"
import type { Product } from "../../types"
import { useData } from "../../contexts/DataContext"
import { useToast } from "../../components/Toast"
import Modal from "./Modal"
import Button from "./Button"
import ProductBasicInfo from "./ProductBasicInfo"
import ProductPricing from "./ProductPricing"
import ProductPackages from "./ProductPackages"
import ProductCustomFields from "./ProductCustomFields"

interface ProductManagementProps {
  isOpen: boolean
  onClose: () => void
  editingProduct?: Product | null
}

type Step = "basic" | "pricing" | "packages" | "fields"

const steps: { key: Step; title: string; description: string }[] = [
  {
    key: "basic",
    title: "المعلومات الأساسية",
    description: "عنوان المنتج والوصف والفئة",
  },
  {
    key: "pricing",
    title: "التسعير",
    description: "إعداد أسعار المنتج الأساسية",
  },
  {
    key: "packages",
    title: "الحزم",
    description: "إدارة حزم المنتج والأكواد الرقمية",
  },
  {
    key: "fields",
    title: "الحقول المخصصة",
    description: "حقول إضافية لجمع معلومات العملاء",
  },
]

export default function ProductManagement({
  isOpen,
  onClose,
  editingProduct,
}: ProductManagementProps) {
  const { addProduct, updateProduct } = useData()
  const toast = useToast()
  const [currentStep, setCurrentStep] = useState<Step>("basic")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState<Partial<Product>>(() => {
    if (editingProduct) {
      return editingProduct
    }
    return {
      title: "",
      description: "",
      category: "",
      tags: [],
      coverImage: "",
      packages: [],
      customFields: [],
      dropdowns: [],
      featured: false,
      popular: false,
    }
  })

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      category: "",
      tags: [],
      coverImage: "",
      packages: [],
      customFields: [],
      dropdowns: [],
      featured: false,
      popular: false,
    })
    setCurrentStep("basic")
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  const handleUpdate = (updates: Partial<Product>) => {
    setFormData((prev) => ({ ...prev, ...updates }))
  }

  const handleSave = async () => {
    setIsSubmitting(true)

    try {
      // Validate required fields
      if (!formData.title || !formData.category || !formData.description || !formData.coverImage) {
        toast.error("يرجى ملء جميع الحقول المطلوبة في المعلومات الأساسية")
        setCurrentStep("basic")
        return
      }

      // Validate packages
      if (!formData.packages || formData.packages.length === 0) {
        toast.error("يجب إضافة حزمة واحدة على الأقل")
        setCurrentStep("packages")
        return
      }

      // Validate package data
      for (const pkg of formData.packages) {
        if (!pkg.name || pkg.price <= 0) {
          toast.error("يرجى ملء جميع بيانات الحزم المطلوبة")
          setCurrentStep("packages")
          return
        }
      }

      let result

      if (editingProduct) {
        // Update existing product
        const updatedProduct = {
          ...formData,
          id: editingProduct.id,
          slug: editingProduct.slug,
          rating: editingProduct.rating,
          commentCount: editingProduct.commentCount,
        } as Product

        result = await updateProduct(updatedProduct)

        if (result.success) {
          toast.success("تم تحديث المنتج بنجاح")
        } else {
          toast.error("فشل في تحديث المنتج", result.error)
        }
      } else {
        // Create new product
        const newProduct: Product = {
          ...formData,
          id: Date.now().toString(),
          slug: formData.title?.toLowerCase().replace(/\s+/g, "-") || "",
          rating: 0,
          commentCount: 0,
        } as Product

        result = await addProduct(newProduct)

        if (result.success) {
          toast.success("تم إضافة المنتج بنجاح")
        } else {
          toast.error("فشل في إضافة المنتج", result.error)
        }
      }

      if (result.success) {
        handleClose()
      }
    } catch (error) {
      toast.error("حدث خطأ غير متوقع")
      console.error("Product save error:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.key === currentStep)
  }

  const canGoNext = () => {
    const currentIndex = getCurrentStepIndex()
    return currentIndex < steps.length - 1
  }

  const canGoPrevious = () => {
    const currentIndex = getCurrentStepIndex()
    return currentIndex > 0
  }

  const goNext = () => {
    if (canGoNext()) {
      const currentIndex = getCurrentStepIndex()
      setCurrentStep(steps[currentIndex + 1].key)
    }
  }

  const goPrevious = () => {
    if (canGoPrevious()) {
      const currentIndex = getCurrentStepIndex()
      setCurrentStep(steps[currentIndex - 1].key)
    }
  }

  const goToStep = (step: Step) => {
    setCurrentStep(step)
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "basic":
        return (
          <ProductBasicInfo
            formData={formData}
            onUpdate={handleUpdate}
            onSave={handleSave}
            onCancel={handleClose}
            isSubmitting={isSubmitting}
            isEditing={!!editingProduct}
          />
        )
      case "pricing":
        return (
          <ProductPricing
            formData={formData}
            onUpdate={handleUpdate}
          />
        )
      case "packages":
        return (
          <ProductPackages
            formData={formData}
            onUpdate={handleUpdate}
          />
        )
      case "fields":
        return (
          <ProductCustomFields
            formData={formData}
            onUpdate={handleUpdate}
          />
        )
      default:
        return null
    }
  }

  const isStepComplete = (step: Step) => {
    switch (step) {
      case "basic":
        return !!(formData.title && formData.category && formData.description && formData.coverImage)
      case "pricing":
        return true // Pricing is optional in current system
      case "packages":
        return formData.packages && formData.packages.length > 0 && 
               formData.packages.every(pkg => pkg.name && pkg.price > 0)
      case "fields":
        return true // Custom fields are optional
      default:
        return false
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="xl"
      title={editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}
    >
      <div className="flex flex-col h-full">
        {/* Step Navigation */}
        <div className="border-b border-gray-700/50 pb-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-white">
              {editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}
            </h2>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-700/50 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* Step Indicators */}
          <div className="flex items-center space-x-4 space-x-reverse overflow-x-auto">
            {steps.map((step, index) => (
              <button
                key={step.key}
                onClick={() => goToStep(step.key)}
                className={`flex items-center space-x-3 space-x-reverse px-4 py-2 rounded-lg transition-all duration-300 whitespace-nowrap ${
                  currentStep === step.key
                    ? "bg-purple-600/20 text-purple-300 border border-purple-600/30"
                    : isStepComplete(step.key)
                    ? "bg-green-600/20 text-green-300 border border-green-600/30 hover:bg-green-600/30"
                    : "bg-gray-700/30 text-gray-400 border border-gray-600/30 hover:bg-gray-700/50"
                }`}
                disabled={isSubmitting}
              >
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                  isStepComplete(step.key) ? "bg-green-500 text-white" : "bg-gray-600 text-gray-300"
                }`}>
                  {isStepComplete(step.key) ? <Check className="w-3 h-3" /> : index + 1}
                </div>
                <div className="text-left">
                  <div className="text-sm font-medium">{step.title}</div>
                  <div className="text-xs opacity-75">{step.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto">
          {renderCurrentStep()}
        </div>

        {/* Navigation Buttons */}
        {currentStep !== "basic" && (
          <div className="flex items-center justify-between pt-6 border-t border-gray-700/50 mt-6">
            <div className="flex space-x-3 space-x-reverse">
              <Button
                onClick={goPrevious}
                variant="secondary"
                disabled={!canGoPrevious() || isSubmitting}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <ChevronRight className="w-4 h-4" />
                <span>السابق</span>
              </Button>

              {canGoNext() ? (
                <Button
                  onClick={goNext}
                  variant="primary"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <span>التالي</span>
                  <ChevronLeft className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  onClick={handleSave}
                  loading={isSubmitting}
                  className="flex items-center space-x-2 space-x-reverse"
                  disabled={!isStepComplete("basic") || !isStepComplete("packages")}
                >
                  <Check className="w-4 h-4" />
                  <span>{editingProduct ? "تحديث المنتج" : "إضافة المنتج"}</span>
                </Button>
              )}
            </div>

            <Button
              onClick={handleClose}
              variant="secondary"
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
          </div>
        )}
      </div>
    </Modal>
  )
}
