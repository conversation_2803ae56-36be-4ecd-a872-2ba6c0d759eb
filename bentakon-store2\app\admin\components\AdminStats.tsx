"use client"

import { Package, Users, ShoppingCart, DollarSign } from "lucide-react"
import { memo } from "react"
import { convertAndFormatPrice } from "../../utils/currency"

interface AdminStatsProps {
  stats: {
    totalProducts: number
    totalUsers: number
    totalOrders: number
    totalRevenue: number
    pendingOrders: number
    completedOrders: number
  }
}

function AdminStats({ stats }: AdminStatsProps) {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-3 md:gap-6">
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-purple-500/10 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-xs md:text-sm">إجمالي المنتجات</p>
            <p className="text-xl md:text-2xl font-bold">{stats.totalProducts}</p>
          </div>
          <Package className="w-6 h-6 md:w-8 md:h-8 text-purple-400" />
        </div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-xs md:text-sm">إجمالي المستخدمين</p>
            <p className="text-xl md:text-2xl font-bold">{stats.totalUsers}</p>
          </div>
          <Users className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
        </div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-green-500/10 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-xs md:text-sm">إجمالي الطلبات</p>
            <p className="text-xl md:text-2xl font-bold">{stats.totalOrders}</p>
          </div>
          <ShoppingCart className="w-6 h-6 md:w-8 md:h-8 text-green-400" />
        </div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-yellow-500/10 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-xs md:text-sm">إجمالي الإيرادات</p>
            <p className="text-xl md:text-2xl font-bold">{convertAndFormatPrice(stats.totalRevenue)}</p>
          </div>
          <DollarSign className="w-6 h-6 md:w-8 md:h-8 text-yellow-400" />
        </div>
      </div>
    </div>
  )
}

export default memo(AdminStats)
