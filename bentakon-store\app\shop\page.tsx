"use client"

import { useState, useMemo, useEffect } from "react"
import { Filter, X, Tag, Search } from "lucide-react"
import Link from "next/link"
import ProductCard from "../components/ProductCard"
import Pagination from "../components/Pagination"
import { LoadingSpinner } from "../components/LoadingStates"
import { useData } from "../contexts/DataContext"
import { useTenant } from "../contexts/TenantContext"
import { getCategories } from "../lib/categories"
import type { Category } from "../types"

export default function ShopPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  const productsPerPage = 20

  // Use centralized data context
  const { products, currentUser } = useData()
  const { tenant } = useTenant()
  const userRole = currentUser?.role || "user"

  // Load categories when component mounts
  useEffect(() => {
    if (tenant) {
      loadCategories()
    }
  }, [tenant])

  const loadCategories = async () => {
    if (!tenant) return

    setCategoriesLoading(true)
    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    } finally {
      setCategoriesLoading(false)
    }
  }

  // Filter products by category and search
  const filteredProducts = useMemo(() => {
    let filtered = [...products]

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(product =>
        product.category_id === selectedCategory
      )
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    return filtered
  }, [products, selectedCategory, searchQuery, categories])

  // Sort products by popularity
  const sortedProducts = useMemo(() => {
    return [...filteredProducts].sort((a, b) => b.commentCount - a.commentCount)
  }, [filteredProducts])

  // Pagination logic
  const { paginatedProducts, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex)
    const totalPages = Math.ceil(sortedProducts.length / productsPerPage)

    return { paginatedProducts, totalPages }
  }, [sortedProducts, currentPage, productsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const clearFilters = () => {
    setSelectedCategory("")
    setSearchQuery("")
    setCurrentPage(1)
  }

  const hasActiveFilters = selectedCategory || searchQuery.trim()

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 md:mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
              متجر المنتجات
            </h1>
            <p className="text-gray-400">
              اكتشف جميع منتجاتنا المتاحة
            </p>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link
              href="/categories"
              className="btn-secondary flex items-center space-x-2 space-x-reverse"
            >
              <Tag className="w-4 h-4" />
              <span>تصفح الفئات</span>
            </Link>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-secondary flex items-center space-x-2 space-x-reverse lg:hidden"
            >
              <Filter className="w-4 h-4" />
              <span>الفلاتر</span>
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar Filters - Desktop */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-6 sticky top-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">الفلاتر</h3>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="text-purple-400 hover:text-purple-300 text-sm transition-colors"
                  >
                    مسح الكل
                  </button>
                )}
              </div>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  البحث
                </label>
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="البحث في المنتجات..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value)
                      setCurrentPage(1)
                    }}
                    className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  />
                </div>
              </div>

              {/* Categories */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  الفئات
                </label>
                {categoriesLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <LoadingSpinner size="sm" className="text-purple-500" />
                  </div>
                ) : (
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        setSelectedCategory("")
                        setCurrentPage(1)
                      }}
                      className={`w-full text-right px-3 py-2 rounded-lg transition-all duration-300 ${
                        !selectedCategory
                          ? 'bg-purple-600/20 text-purple-400 border border-purple-500/30'
                          : 'text-gray-300 hover:bg-gray-700/50'
                      }`}
                    >
                      جميع المنتجات ({products.length})
                    </button>

                    {categories.map((category) => {
                      const productCount = products.filter(product =>
                        product.category_id === category.id
                      ).length

                      return (
                        <button
                          key={category.id}
                          onClick={() => {
                            setSelectedCategory(category.id)
                            setCurrentPage(1)
                          }}
                          className={`w-full text-right px-3 py-2 rounded-lg transition-all duration-300 ${
                            selectedCategory === category.id
                              ? 'bg-purple-600/20 text-purple-400 border border-purple-500/30'
                              : 'text-gray-300 hover:bg-gray-700/50'
                          }`}
                        >
                          {category.name || category.slug} ({productCount})
                        </button>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Mobile Filters Overlay */}
          {showFilters && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 lg:hidden">
              <div className="fixed bottom-0 left-0 right-0 bg-gray-800/95 backdrop-blur-md rounded-t-2xl border-t border-gray-700/50 p-6 max-h-[80vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white">الفلاتر</h3>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="p-2 text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Mobile Search */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    البحث
                  </label>
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="البحث في المنتجات..."
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value)
                        setCurrentPage(1)
                      }}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </div>
                </div>

                {/* Mobile Categories */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    الفئات
                  </label>
                  <div className="grid grid-cols-1 gap-2">
                    <button
                      onClick={() => {
                        setSelectedCategory("")
                        setCurrentPage(1)
                        setShowFilters(false)
                      }}
                      className={`text-right px-4 py-3 rounded-lg transition-all duration-300 ${
                        !selectedCategory
                          ? 'bg-purple-600/20 text-purple-400 border border-purple-500/30'
                          : 'text-gray-300 hover:bg-gray-700/50'
                      }`}
                    >
                      جميع المنتجات ({products.length})
                    </button>

                    {categories.map((category) => {
                      const productCount = products.filter(product =>
                        product.category_id === category.id
                      ).length

                      return (
                        <button
                          key={category.id}
                          onClick={() => {
                            setSelectedCategory(category.id)
                            setCurrentPage(1)
                            setShowFilters(false)
                          }}
                          className={`text-right px-4 py-3 rounded-lg transition-all duration-300 ${
                            selectedCategory === category.id
                              ? 'bg-purple-600/20 text-purple-400 border border-purple-500/30'
                              : 'text-gray-300 hover:bg-gray-700/50'
                          }`}
                        >
                          {category.name || category.slug} ({productCount})
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Mobile Filter Actions */}
                <div className="flex space-x-3 space-x-reverse">
                  {hasActiveFilters && (
                    <button
                      onClick={() => {
                        clearFilters()
                        setShowFilters(false)
                      }}
                      className="flex-1 btn-secondary"
                    >
                      مسح الفلاتر
                    </button>
                  )}
                  <button
                    onClick={() => setShowFilters(false)}
                    className="flex-1 btn-primary"
                  >
                    تطبيق الفلاتر
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <p className="text-gray-400">
                  {hasActiveFilters
                    ? `${sortedProducts.length} منتج مطابق للفلاتر`
                    : `${products.length} منتج متاح`
                  }
                </p>
                {hasActiveFilters && (
                  <div className="flex items-center space-x-2 space-x-reverse mt-2">
                    <span className="text-sm text-gray-500">الفلاتر النشطة:</span>
                    {selectedCategory && (
                      <span className="inline-flex items-center px-2 py-1 bg-purple-600/20 text-purple-400 rounded-lg text-xs">
                        {categories.find(cat => cat.id === selectedCategory)?.name || 'فئة'}
                        <button
                          onClick={() => setSelectedCategory("")}
                          className="mr-1 hover:text-purple-300"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    )}
                    {searchQuery && (
                      <span className="inline-flex items-center px-2 py-1 bg-purple-600/20 text-purple-400 rounded-lg text-xs">
                        "{searchQuery}"
                        <button
                          onClick={() => setSearchQuery("")}
                          className="mr-1 hover:text-purple-300"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Products Grid */}
            {paginatedProducts.length === 0 ? (
              <div className="text-center py-16">
                <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-300 mb-2">
                  {hasActiveFilters ? 'لا توجد منتجات مطابقة' : 'لا توجد منتجات متاحة'}
                </h3>
                <p className="text-gray-400 mb-6">
                  {hasActiveFilters
                    ? 'جرب تعديل الفلاتر أو البحث بكلمات مختلفة'
                    : 'لم يتم إضافة منتجات بعد'
                  }
                </p>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="btn-secondary"
                  >
                    مسح جميع الفلاتر
                  </button>
                )}
              </div>
            ) : (
              <>
                {/* Products Grid - Same responsive pattern */}
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3 md:gap-4 mb-8">
                  {paginatedProducts.map((product) => (
                    <ProductCard key={product.id} product={product} userRole={userRole} />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    className="mt-8"
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
