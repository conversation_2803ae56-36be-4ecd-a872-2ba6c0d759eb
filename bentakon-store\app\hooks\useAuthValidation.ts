import { z } from 'zod'

// Validation schemas for authentication forms
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('البريد الإلكتروني غير صحيح'),
  password: z
    .string()
    .min(1, 'كلمة المرور مطلوبة')
    .min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/,
      'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص'),
  rememberMe: z.boolean().optional()
})

export const registerSchema = z.object({
  name: z
    .string()
    .min(1, 'الاسم مطلوب')
    .min(2, 'الاسم يجب أن يكون أكثر من حرفين')
    .max(50, 'الاسم طويل جداً'),
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('البريد الإلكتروني غير صحيح'),
  phone: z
    .string()
    .regex(/^[0-9+\-\s()]*$/, 'رقم الهاتف غير صحيح')
    .optional()
    .or(z.literal('')),
  password: z
    .string()
    .min(1, 'كلمة المرور مطلوبة')
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .max(100, 'كلمة المرور طويلة جداً'),
  confirmPassword: z
    .string()
    .min(1, 'تأكيد كلمة المرور مطلوب'),
  agreeToTerms: z
    .boolean()
    .refine(val => val === true, 'يجب الموافقة على الشروط والأحكام'),
  agreeToMarketing: z.boolean().optional()
}).refine(data => data.password === data.confirmPassword, {
  message: 'كلمات المرور غير متطابقة',
  path: ['confirmPassword']
})

export const resetPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('البريد الإلكتروني غير صحيح')
})

export const changePasswordSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'كلمة المرور الحالية مطلوبة'),
  newPassword: z
    .string()
    .min(1, 'كلمة المرور الجديدة مطلوبة')
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .max(100, 'كلمة المرور طويلة جداً'),
  confirmNewPassword: z
    .string()
    .min(1, 'تأكيد كلمة المرور الجديدة مطلوب')
}).refine(data => data.newPassword === data.confirmNewPassword, {
  message: 'كلمات المرور غير متطابقة',
  path: ['confirmNewPassword']
})

export const updateProfileSchema = z.object({
  name: z
    .string()
    .min(1, 'الاسم مطلوب')
    .min(2, 'الاسم يجب أن يكون أكثر من حرفين')
    .max(50, 'الاسم طويل جداً'),
  phone: z
    .string()
    .regex(/^[0-9+\-\s()]*$/, 'رقم الهاتف غير صحيح')
    .optional()
    .or(z.literal(''))
})

// Type exports
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
export type UpdateProfileFormData = z.infer<typeof updateProfileSchema>

// Validation hook
export function useAuthValidation() {
  const validateLogin = (data: unknown) => {
    try {
      return { success: true, data: loginSchema.parse(data), errors: [] }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          data: null,
          errors: error.errors.map(err => err.message)
        }
      }
      return {
        success: false,
        data: null,
        errors: ['حدث خطأ في التحقق من البيانات']
      }
    }
  }

  const validateRegister = (data: unknown) => {
    try {
      return { success: true, data: registerSchema.parse(data), errors: [] }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          data: null,
          errors: error.errors.map(err => err.message)
        }
      }
      return {
        success: false,
        data: null,
        errors: ['حدث خطأ في التحقق من البيانات']
      }
    }
  }

  const validateResetPassword = (data: unknown) => {
    try {
      return { success: true, data: resetPasswordSchema.parse(data), errors: [] }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          data: null,
          errors: error.errors.map(err => err.message)
        }
      }
      return {
        success: false,
        data: null,
        errors: ['حدث خطأ في التحقق من البيانات']
      }
    }
  }

  const validateChangePassword = (data: unknown) => {
    try {
      return { success: true, data: changePasswordSchema.parse(data), errors: [] }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          data: null,
          errors: error.errors.map(err => err.message)
        }
      }
      return {
        success: false,
        data: null,
        errors: ['حدث خطأ في التحقق من البيانات']
      }
    }
  }

  const validateUpdateProfile = (data: unknown) => {
    try {
      return { success: true, data: updateProfileSchema.parse(data), errors: [] }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          data: null,
          errors: error.errors.map(err => err.message)
        }
      }
      return {
        success: false,
        data: null,
        errors: ['حدث خطأ في التحقق من البيانات']
      }
    }
  }

  return {
    validateLogin,
    validateRegister,
    validateResetPassword,
    validateChangePassword,
    validateUpdateProfile
  }
}
