"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '../../contexts/AuthContext'
import { Mail, Loader2, X, ArrowRight } from 'lucide-react'
import { toast } from 'sonner'

interface ForgotPasswordModalProps {
  isOpen: boolean
  onClose: () => void
  onSwitchToLogin: () => void
}

export default function ForgotPasswordModal({ 
  isOpen, 
  onClose, 
  onSwitchToLogin 
}: ForgotPasswordModalProps) {
  const { resetPassword, authState, clearError } = useAuth()
  const [email, setEmail] = useState('')
  const [isEmailSent, setIsEmailSent] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      toast.error('يرجى إدخال البريد الإلكتروني')
      return
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error('البريد الإلكتروني غير صحيح')
      return
    }

    try {
      await resetPassword(email)
      setIsEmailSent(true)
      toast.success('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني')
    } catch (error) {
      toast.error(authState.error || 'حدث خطأ أثناء إرسال رابط إعادة التعيين')
    }
  }

  const handleInputChange = (value: string) => {
    setEmail(value)
    if (authState.error) {
      clearError()
    }
  }

  const handleClose = () => {
    clearError()
    setEmail('')
    setIsEmailSent(false)
    onClose()
  }

  const handleBackToLogin = () => {
    handleClose()
    onSwitchToLogin()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md mx-auto bg-gray-900/95 backdrop-blur-md border border-gray-700/50 shadow-2xl">
        {/* Custom Header with Close Button */}
        <div className="flex items-center justify-between p-6 pb-4">
          <DialogTitle className="text-xl font-bold text-white">
            {isEmailSent ? 'تم إرسال الرابط' : 'نسيت كلمة المرور؟'}
          </DialogTitle>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
            aria-label="إغلاق"
          >
            <X className="w-5 h-5 text-gray-400 hover:text-white" />
          </button>
        </div>

        <div className="px-6 pb-6">
          {!isEmailSent ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center">
                <p className="text-sm text-gray-400 mb-6">
                  أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور
                </p>
              </div>

              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="reset-email" className="text-sm font-medium text-gray-200">
                  البريد الإلكتروني
                </Label>
                <div className="relative">
                  <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    id="reset-email"
                    type="email"
                    value={email}
                    onChange={(e) => handleInputChange(e.target.value)}
                    placeholder="أدخل بريدك الإلكتروني"
                    className="pr-10 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-500/50 focus:ring-purple-500/50"
                    disabled={authState.isLoading}
                    required
                  />
                </div>
              </div>

              {/* Error Message */}
              {authState.error && (
                <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
                  {authState.error}
                </div>
              )}

              {/* Reset Button */}
              <Button
                type="submit"
                disabled={authState.isLoading}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {authState.isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    جاري الإرسال...
                  </>
                ) : (
                  'إرسال رابط إعادة التعيين'
                )}
              </Button>

              {/* Back to Login Link */}
              <div className="text-center pt-4 border-t border-gray-700/50">
                <button
                  type="button"
                  onClick={handleBackToLogin}
                  className="inline-flex items-center space-x-2 space-x-reverse text-sm text-purple-400 hover:text-purple-300 font-medium transition-colors duration-200"
                  disabled={authState.isLoading}
                >
                  <ArrowRight className="w-4 h-4" />
                  <span>العودة إلى تسجيل الدخول</span>
                </button>
              </div>
            </form>
          ) : (
            /* Success State */
            <div className="text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-green-500/20 rounded-full flex items-center justify-center">
                <Mail className="w-8 h-8 text-green-400" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">تم إرسال الرابط بنجاح!</h3>
                <p className="text-sm text-gray-400">
                  تم إرسال رابط إعادة تعيين كلمة المرور إلى:
                </p>
                <p className="text-sm font-medium text-purple-400">{email}</p>
              </div>

              <div className="p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <p className="text-sm text-blue-400">
                  تحقق من صندوق الوارد الخاص بك واتبع التعليمات في الرسالة لإعادة تعيين كلمة المرور.
                  إذا لم تجد الرسالة، تحقق من مجلد الرسائل غير المرغوب فيها.
                </p>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleBackToLogin}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
                >
                  العودة إلى تسجيل الدخول
                </Button>
                
                <button
                  onClick={() => setIsEmailSent(false)}
                  className="w-full text-sm text-gray-400 hover:text-gray-300 transition-colors duration-200"
                >
                  إرسال إلى بريد إلكتروني آخر
                </button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
