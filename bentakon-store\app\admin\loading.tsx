import { StatsSkeleton, TableSkeleton } from "../components/LoadingStates"

export default function AdminLoading() {
  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="h-8 bg-gray-800 rounded w-64 mb-4 animate-pulse"></div>
          <div className="h-4 bg-gray-800 rounded w-96 animate-pulse"></div>
        </div>

        {/* Stats Skeleton */}
        <StatsSkeleton />

        {/* Content Skeleton */}
        <div className="mt-8 bg-gray-800 rounded-xl p-6">
          <div className="h-6 bg-gray-700 rounded w-48 mb-6 animate-pulse"></div>
          <TableSkeleton rows={8} columns={5} />
        </div>
      </div>
    </div>
  )
}
