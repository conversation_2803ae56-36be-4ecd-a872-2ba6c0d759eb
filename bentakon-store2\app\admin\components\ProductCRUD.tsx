"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, Key, AlertCircle } from "lucide-react"
import type { Product } from "../../types"
import { useData } from "../../contexts/DataContext"
import { useToast } from "../../components/Toast"
import { ProductGridSkeleton } from "../../components/LoadingStates"
import ConfirmDialog from "./ConfirmDialog"
import ProductManagement from "./ProductManagement"

export default function ProductCRUD() {
  // Use centralized data context
  const { products, deleteProduct, isLoading, error } = useData()
  const toast = useToast()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [deleteConfirm, setDeleteConfirm] = useState<{ isOpen: boolean; productId: string; productName: string }>({
    isOpen: false,
    productId: "",
    productName: "",
  })

  const openModal = (product?: Product) => {
    setEditingProduct(product || null)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setEditingProduct(null)
  }

  const handleDeleteClick = (product: Product) => {
    setDeleteConfirm({
      isOpen: true,
      productId: product.id,
      productName: product.title,
    })
  }

  const handleDeleteConfirm = async () => {
    try {
      const result = await deleteProduct(deleteConfirm.productId)

      if (result.success) {
        toast.success('تم حذف المنتج بنجاح')
      } else {
        toast.error('فشل في حذف المنتج', result.error)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
      console.error('Product delete error:', error)
    } finally {
      setDeleteConfirm({ isOpen: false, productId: "", productName: "" })
    }
  }



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-xl md:text-2xl font-bold">إدارة المنتجات</h2>
        <button onClick={() => openModal()} className="btn-primary flex items-center justify-center space-x-2 space-x-reverse w-full sm:w-auto">
          <Plus className="w-5 h-5" />
          <span>إضافة منتج</span>
        </button>
      </div>

      {/* Products Display */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-right text-gray-400 text-sm">
                  <th className="pb-3">المنتج</th>
                  <th className="pb-3">الفئة</th>
                  <th className="pb-3">الحزم</th>
                  <th className="pb-3">الأكواد الرقمية</th>
                  <th className="pb-3">التقييم</th>
                  <th className="pb-3">الحالة</th>
                  <th className="pb-3">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => {
                  const totalCodes = product.packages.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0)
                  const availableCodes = product.packages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)
                  const hasDigitalPackages = product.packages.some((pkg) => pkg.hasDigitalCodes)

                  return (
                    <tr key={product.id} className="border-t border-gray-700/50">
                      <td className="py-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <img
                            src={product.coverImage || "/logo.jpg"}
                            alt={product.title}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                          <div>
                            <p className="font-semibold">{product.title}</p>
                            <p className="text-sm text-gray-400">{product.slug}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4">{product.category}</td>
                      <td className="py-4">{product.packages.length} حزمة</td>
                      <td className="py-4">
                        {hasDigitalPackages ? (
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Key className="w-4 h-4 text-blue-400" />
                            <span className="text-sm">
                              {availableCodes}/{totalCodes}
                            </span>
                            {availableCodes === 0 && totalCodes > 0 && (
                              <AlertCircle className="w-4 h-4 text-red-400" title="نفدت الأكواد" />
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">لا يوجد</span>
                        )}
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span>{product.rating}</span>
                          <span className="text-gray-400">({product.commentCount})</span>
                        </div>
                      </td>
                      <td className="py-4">
                        <div className="flex space-x-2 space-x-reverse">
                          {product.featured && (
                            <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-xl text-xs">مميز</span>
                          )}
                          {product.popular && (
                            <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-xl text-xs">شائع</span>
                          )}
                        </div>
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openModal(product)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(product)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-xl hover:bg-red-400/10"
                            disabled={isLoading}
                            title="حذف"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden space-y-4">
            {products.map((product) => {
              const totalCodes = product.packages.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0)
              const availableCodes = product.packages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)
              const hasDigitalPackages = product.packages.some((pkg) => pkg.hasDigitalCodes)

              return (
                <div key={product.id} className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                  {/* Product Header */}
                  <div className="flex items-start space-x-3 space-x-reverse mb-4">
                    <img
                      src={product.coverImage || "/logo.jpg"}
                      alt={product.title}
                      className="w-16 h-16 rounded-xl object-cover flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg truncate">{product.title}</h3>
                      <p className="text-sm text-gray-400 truncate">{product.slug}</p>
                      <div className="flex items-center space-x-2 space-x-reverse mt-2">
                        {product.featured && (
                          <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-lg text-xs">مميز</span>
                        )}
                        {product.popular && (
                          <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-lg text-xs">شائع</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Product Details Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الفئة</p>
                      <p className="text-sm font-medium">{product.category}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الحزم</p>
                      <p className="text-sm font-medium">{product.packages.length} حزمة</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">التقييم</p>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-sm font-medium">{product.rating}</span>
                        <span className="text-xs text-gray-400">({product.commentCount})</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الأكواد الرقمية</p>
                      {hasDigitalPackages ? (
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Key className="w-3 h-3 text-blue-400" />
                          <span className="text-sm font-medium">
                            {availableCodes}/{totalCodes}
                          </span>
                          {availableCodes === 0 && totalCodes > 0 && (
                            <AlertCircle className="w-3 h-3 text-red-400" title="نفدت الأكواد" />
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">لا يوجد</span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-3 space-x-reverse pt-3 border-t border-gray-600/50">
                    <button
                      onClick={() => openModal(product)}
                      className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 transition-colors rounded-xl"
                    >
                      <Edit className="w-4 h-4" />
                      <span className="text-sm font-medium">تعديل</span>
                    </button>
                    <button
                      onClick={() => handleDeleteClick(product)}
                      className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors rounded-xl"
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4" />
                      <span className="text-sm font-medium">حذف</span>
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Product Management Modal */}
      <ProductManagement
        isOpen={isModalOpen}
        onClose={closeModal}
        editingProduct={editingProduct}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm.isOpen}
        onClose={() => setDeleteConfirm({ isOpen: false, productId: "", productName: "" })}
        onConfirm={handleDeleteConfirm}
        title="تأكيد حذف المنتج"
        message={`هل أنت متأكد من حذف المنتج "${deleteConfirm.productName}"؟ لا يمكن التراجع عن هذا الإجراء.`}
        confirmText="حذف"
        cancelText="إلغاء"
        variant="danger"
      />
    </div>
  )
}
