"use client"

import { useState } from "react"
import { useAuth } from "../contexts/AuthContext"
import { upsertUserProfile } from "../lib/supabase"

export default function AdminSetup() {
  const { authState } = useAuth()
  const [isUpdating, setIsUpdating] = useState(false)
  const [message, setMessage] = useState("")

  const makeCurrentUserAdmin = async () => {
    if (!authState.user) {
      setMessage("❌ يجب تسجيل الدخول أولاً")
      return
    }

    setIsUpdating(true)
    setMessage("⏳ جاري تحديث الصلاحيات...")

    try {
      const result = await upsertUserProfile(authState.user.id, {
        name: authState.user.name,
        role: 'admin',
        wallet_balance: authState.user.walletBalance || 0
      })

      if (result) {
        setMessage("✅ تم! أنت الآن مدير. قم بتحديث الصفحة لرؤية لوحة التحكم")
        // Force a page reload to update the user role in the UI
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      } else {
        setMessage("❌ فشل في تحديث الصلاحيات")
      }
    } catch (error) {
      console.error('Error making user admin:', error)
      setMessage("❌ حدث خطأ أثناء التحديث")
    } finally {
      setIsUpdating(false)
    }
  }

  // Only show this component in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-50">
      <h3 className="text-white font-bold mb-2">🛠️ إعداد المطور</h3>
      
      {authState.user ? (
        <div className="space-y-2">
          <p className="text-gray-300 text-sm">
            المستخدم: {authState.user.name}
          </p>
          <p className="text-gray-300 text-sm">
            الدور الحالي: {authState.user.role || 'user'}
          </p>
          
          {authState.user.role !== 'admin' && (
            <button
              onClick={makeCurrentUserAdmin}
              disabled={isUpdating}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
            >
              {isUpdating ? "⏳ جاري التحديث..." : "🔑 جعلني مدير"}
            </button>
          )}
          
          {authState.user.role === 'admin' && (
            <div className="text-green-400 text-sm">
              ✅ أنت مدير بالفعل!
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-300 text-sm">
          يجب تسجيل الدخول أولاً
        </p>
      )}
      
      {message && (
        <div className="mt-2 p-2 bg-gray-700 rounded text-sm text-white">
          {message}
        </div>
      )}
    </div>
  )
}
