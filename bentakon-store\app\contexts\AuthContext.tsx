"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase, signInWithEmail, signUpWithEmail, resetPassword, signOut, getUserProfile, upsertUserProfile } from '../lib/supabase'
import { useTenant } from './TenantContext'
import type { User } from '../types'
import type { User as SupabaseUser } from '@supabase/supabase-js'

// Authentication types
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  name: string
  phone?: string
  agreeToTerms: boolean
  agreeToMarketing?: boolean
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export type AuthModalType = 'login' | 'register' | 'forgot-password' | null

export interface AuthContextType {
  // State
  authState: AuthState
  currentModal: AuthModalType
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  
  // Modal controls
  openModal: (type: AuthModalType) => void
  closeModal: () => void
  
  // Utility functions
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { tenant } = useTenant()
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [currentModal, setCurrentModal] = useState<AuthModalType>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Performance optimization: Cache user profiles to avoid repeated DB queries
  const [profileCache, setProfileCache] = useState<Map<string, User>>(new Map())
  const [lastTenantId, setLastTenantId] = useState<string | null>(null)

  // Auth state
  const authState: AuthState = {
    user: currentUser,
    isLoading,
    isAuthenticated: !!currentUser,
    error
  }

  // Clear error function
  const clearError = () => setError(null)

  // Optimized auth initialization with debouncing and caching
  useEffect(() => {
    let subscription: any = null
    let timeoutId: NodeJS.Timeout | null = null

    const initializeAuth = async () => {
      // Prevent redundant initialization if tenant hasn't changed
      if (tenant?.id === lastTenantId && currentUser) {
        console.log('Skipping auth re-initialization - tenant unchanged')
        setIsLoading(false)
        return
      }

      // Get initial session
      const { data: { session } } = await supabase.auth.getSession()
      // Security: Don't log sensitive user data in production
      if (process.env.NODE_ENV === 'development') {
        console.log('Initial session check - User exists:', !!session?.user, 'Tenant available:', !!tenant)
      }

      if (session?.user && tenant) {
        await loadUserProfile(session.user)
        setLastTenantId(tenant.id)
      } else {
        setIsLoading(false)
      }

      // Listen for auth changes (only set up once)
      if (!subscription) {
        const { data } = supabase.auth.onAuthStateChange(async (event, session) => {
          // Security: Don't log sensitive user data in production
        if (process.env.NODE_ENV === 'development') {
          console.log('Auth state change:', event, 'User exists:', !!session?.user)
        }

          if (event === 'SIGNED_IN' && session?.user && tenant) {
            await loadUserProfile(session.user)
          } else if (event === 'SIGNED_OUT') {
            setCurrentUser(null)
            setProfileCache(new Map()) // Clear cache on logout
            setLastTenantId(null)
            setIsLoading(false)
          } else if (event === 'TOKEN_REFRESHED' && session?.user && tenant) {
            // Use cached profile for token refresh to avoid DB query
            const cacheKey = `${session.user.id}-${tenant.id}`
            if (!profileCache.has(cacheKey)) {
              await loadUserProfile(session.user)
            }
          } else {
            setIsLoading(false)
          }
        })

        subscription = data.subscription
      }
    }

    // Debounce initialization to prevent rapid re-initializations
    if (tenant) {
      timeoutId = setTimeout(() => {
        initializeAuth()
      }, 100) // 100ms debounce
    } else {
      setIsLoading(false)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (subscription) {
        subscription.unsubscribe()
        subscription = null
      }
    }
  }, [tenant?.id, lastTenantId])

  // Optimized user profile loading with caching
  const loadUserProfile = async (authUser: SupabaseUser, forceReload = false) => {
    try {
      if (!tenant) {
        console.warn('No tenant available for profile loading')
        setIsLoading(false)
        return
      }

      // Check cache first to avoid unnecessary database queries
      const cacheKey = `${authUser.id}-${tenant.id}`
      const cachedProfile = profileCache.get(cacheKey)

      if (cachedProfile && !forceReload) {
        console.log('Using cached profile for:', authUser.email)
        setCurrentUser(cachedProfile)
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      // Security: Don't log sensitive user data in production
      if (process.env.NODE_ENV === 'development') {
        console.log('Loading user profile - User exists:', !!authUser, 'in tenant:', tenant?.name)
      }

      // Try to get the tenant-specific profile
      let profile = null
      let retries = 3

      while (!profile && retries > 0) {
        // Get profile for current tenant
        const { data } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', authUser.id)
          .eq('tenant_id', tenant.id)
          .single()

        profile = data
        if (!profile) {
          await new Promise(resolve => setTimeout(resolve, 1000))
          retries--
        }
      }

      if (!profile) {
        // Profile doesn't exist for this tenant - user needs to sign out
        console.log('No profile found for user in current tenant, signing out')
        setCurrentUser(null)
        await signOut()
        setError('لا يوجد حساب لك في هذا المتجر. يرجى إنشاء حساب جديد.')
        setIsLoading(false)
        return
      }

      if (profile) {
        const user: User = {
          id: authUser.id,
          email: authUser.email || '',
          name: profile.name || authUser.user_metadata?.name || '',
          role: profile.role || 'user',
          walletBalance: Number(profile.wallet_balance) || 0,
          avatar: profile.avatar || authUser.user_metadata?.avatar_url,
          phone: profile.phone || authUser.user_metadata?.phone,
          createdAt: profile.created_at || new Date().toISOString()
        }

        // Cache the profile to avoid future database queries
        const cacheKey = `${authUser.id}-${tenant.id}`
        setProfileCache(prev => new Map(prev.set(cacheKey, user)))

        console.log('Setting current user:', user.name, user.email)
        setCurrentUser(user)
        console.log('✅ Auth: Current user set successfully:', user.email)
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في تحميل ملف المستخدم'
      setError(errorMessage)
      setCurrentUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  // Login function with tenant awareness
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()
      if (process.env.NODE_ENV === 'development') {
        console.log('Attempting login for:', credentials.email, 'in tenant:', tenant?.name)
      }

      if (!tenant) {
        throw new Error('لم يتم تحديد المتجر')
      }

      const result = await signInWithEmail(credentials.email, credentials.password, tenant.id)
      if (process.env.NODE_ENV === 'development') {
        console.log('Login result:', result)
      }

      if (!result.success) {
        throw new Error(result.error || 'البريد الإلكتروني أو كلمة المرور غير صحيحة')
      }

      // User profile will be loaded automatically by the auth state change listener
      setCurrentModal(null)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدخول'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Register function with tenant awareness
  const register = async (data: RegisterData): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      if (!tenant) {
        throw new Error('لم يتم تحديد المتجر')
      }

      // Validate passwords match
      if (data.password !== data.confirmPassword) {
        throw new Error('كلمات المرور غير متطابقة')
      }

      // Validate terms agreement
      if (!data.agreeToTerms) {
        throw new Error('يجب الموافقة على الشروط والأحكام')
      }

      const result = await signUpWithEmail(data.email, data.password, data.name, tenant.id)

      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ أثناء إنشاء الحساب')
      }

      // User profile will be created automatically by the signup function
      setCurrentModal(null)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الحساب'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true)

      const result = await signOut()

      if (!result.success) {
        console.error('Logout error:', result.error)
      }

      // User state will be cleared automatically by the auth state change listener

    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Reset password function
  const resetPasswordFunction = async (email: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const result = await resetPassword(email)

      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Modal controls
  const openModal = (type: AuthModalType) => {
    clearError()
    setCurrentModal(type)
  }

  const closeModal = () => {
    clearError()
    setCurrentModal(null)
  }

  const value: AuthContextType = {
    authState,
    currentModal,
    login,
    register,
    logout,
    resetPassword: resetPasswordFunction,
    openModal,
    closeModal,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
