"use client"

import { useState, useMemo } from "react"
import { Users, Package, BarChart3, DollarSign, ShoppingCart, Home, Menu, X } from "lucide-react"
import {
  LazyProductCRUD,
  LazyUserManagement,
  LazyHomepageManagement,
  LazyOrderManagement
} from "../components/LazyComponents"
import { convertAndFormatPrice } from "../utils/currency"
import { useData } from "../contexts/DataContext"
import { useRequireAdmin } from "../hooks/useProtectedRoute"
import AdminNavigation from "./components/AdminNavigation"
import AdminOverview from "./components/AdminOverview"

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview")
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Use authentication guard for admin access
  const { isLoading, isAuthenticated, user, hasRequiredRole } = useRequireAdmin()

  // Use centralized data context
  const { products, users, orders } = useData()

  // Define tabs
  const tabs = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3 },
    { id: "products", label: "المنتجات", icon: Package },
    { id: "users", label: "المستخدمون", icon: Users },
    { id: "orders", label: "الطلبات", icon: ShoppingCart },
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home },
  ]

  // Memoize expensive calculations - moved before early returns
  const stats = useMemo(() => ({
    totalProducts: products.length,
    totalUsers: users.length,
    totalOrders: orders.length,
    totalRevenue: orders.reduce((sum, order) => sum + order.amount, 0),
    pendingOrders: orders.filter((o) => o.status === "pending").length,
    completedOrders: orders.filter((o) => o.status === "completed").length,
  }), [products.length, users.length, orders])

  // Memoize recent orders - moved before early returns
  const recentOrders = useMemo(() =>
    orders
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10),
    [orders]
  )

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-gray-400">جاري التحقق من الصلاحيات...</p>
      </div>
    )
  }

  // Authentication guard will handle redirects, but show fallback just in case
  if (!isAuthenticated || !hasRequiredRole) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">الوصول مرفوض</h1>
        <p className="text-gray-400">ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-4 md:py-8">
      {/* Header */}
      <div className="mb-6 md:mb-8">
        <div>
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 md:mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            لوحة التحكم الإدارية
          </h1>
          <p className="text-gray-400 text-sm md:text-lg">إدارة متجرك والمنتجات والمستخدمين والصفحة الرئيسية</p>
        </div>
      </div>

      {/* Navigation */}
      <AdminNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />

      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6 lg:gap-8">
        {/* Desktop Sidebar Navigation */}
        <div className="hidden lg:block lg:col-span-1">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-4 shadow-xl sticky top-6">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Tab Bar */}
        <div className="lg:hidden order-last">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-2 shadow-xl">
            <div className="grid grid-cols-4 gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex flex-col items-center space-y-1 px-2 py-3 rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="text-xs font-medium">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 min-h-0">
          {activeTab === "overview" && (
            <AdminOverview stats={stats} recentOrders={recentOrders} />
          )}

          {activeTab === "products" && <LazyProductCRUD />}

          {activeTab === "users" && <LazyUserManagement />}

          {activeTab === "orders" && <LazyOrderManagement />}

          {activeTab === "homepage" && <LazyHomepageManagement />}
        </div>
      </div>
    </div>
  )
}
