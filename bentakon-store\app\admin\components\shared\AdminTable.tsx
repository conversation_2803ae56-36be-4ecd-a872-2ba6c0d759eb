import React from 'react'
import { Eye, Edit, Trash2 } from 'lucide-react'

export interface ColumnDefinition<T> {
  key: keyof T | string
  label: string
  render?: (item: T) => React.ReactNode
  className?: string
  mobileHidden?: boolean
}

export interface AdminTableProps<T extends { id: string }> {
  items: T[]
  columns: ColumnDefinition<T>[]
  loading: boolean
  actionLoading: string | null
  onView?: (item: T) => void
  onEdit?: (item: T) => void
  onDelete?: (item: T) => void
  renderMobileCard: (item: T) => React.ReactNode
  emptyState?: {
    icon: React.ReactNode
    title: string
    description: string
    action?: React.ReactNode
  }
}

export default function AdminTable<T extends { id: string }>({
  items,
  columns,
  loading,
  actionLoading,
  onView,
  onEdit,
  onDelete,
  renderMobileCard,
  emptyState
}: AdminTableProps<T>) {
  if (loading) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-16 bg-gray-700/50 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (items.length === 0 && emptyState) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        <div className="text-center py-12 px-6">
          <div className="text-6xl mb-4">{emptyState.icon}</div>
          <h3 className="text-xl font-semibold mb-2">{emptyState.title}</h3>
          <p className="text-gray-400 mb-6">{emptyState.description}</p>
          {emptyState.action}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
      {/* Mobile Cards */}
      <div className="md:hidden space-y-3 p-4">
        {items.map((item) => (
          <div key={item.id} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30">
            {renderMobileCard(item)}
            
            {/* Mobile Action Buttons */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-600/30 mt-3">
              {onView && (
                <button
                  onClick={() => onView(item)}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-600/20 text-blue-400 rounded text-sm hover:bg-blue-600/30 transition-colors"
                >
                  <Eye className="w-3 h-3" />
                  تفاصيل
                </button>
              )}
              
              <div className="flex gap-2">
                {onEdit && (
                  <button
                    onClick={() => onEdit(item)}
                    disabled={actionLoading === item.id}
                    className="flex items-center gap-1 px-3 py-1 bg-purple-600/20 text-purple-400 rounded text-sm hover:bg-purple-600/30 transition-colors disabled:opacity-50"
                  >
                    {actionLoading === item.id ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-purple-400"></div>
                    ) : (
                      <Edit className="w-3 h-3" />
                    )}
                    تعديل
                  </button>
                )}
                
                {onDelete && (
                  <button
                    onClick={() => onDelete(item)}
                    disabled={actionLoading === item.id}
                    className="flex items-center gap-1 px-3 py-1 bg-red-600/20 text-red-400 rounded text-sm hover:bg-red-600/30 transition-colors disabled:opacity-50"
                  >
                    {actionLoading === item.id ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-red-400"></div>
                    ) : (
                      <Trash2 className="w-3 h-3" />
                    )}
                    حذف
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-right text-gray-400 text-sm border-b border-gray-700/50">
              {columns.map((column, index) => (
                <th key={index} className={`p-4 ${column.className || ''}`}>
                  {column.label}
                </th>
              ))}
              {(onView || onEdit || onDelete) && (
                <th className="p-4">الإجراءات</th>
              )}
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr key={item.id} className="border-b border-gray-700/50 hover:bg-gray-700/20">
                {columns.map((column, index) => (
                  <td key={index} className={`p-4 ${column.className || ''}`}>
                    {column.render 
                      ? column.render(item)
                      : String((item as any)[column.key] || '')
                    }
                  </td>
                ))}
                
                {(onView || onEdit || onDelete) && (
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      {onView && (
                        <button
                          onClick={() => onView(item)}
                          className="p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-lg hover:bg-blue-400/10"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}
                      
                      {onEdit && (
                        <button
                          onClick={() => onEdit(item)}
                          disabled={actionLoading === item.id}
                          className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-lg hover:bg-purple-400/10 disabled:opacity-50"
                          title="تعديل"
                        >
                          {actionLoading === item.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b border-purple-400"></div>
                          ) : (
                            <Edit className="w-4 h-4" />
                          )}
                        </button>
                      )}
                      
                      {onDelete && (
                        <button
                          onClick={() => onDelete(item)}
                          disabled={actionLoading === item.id}
                          className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-lg hover:bg-red-400/10 disabled:opacity-50"
                          title="حذف"
                        >
                          {actionLoading === item.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b border-red-400"></div>
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </button>
                      )}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
