"use client"

import { ReactNode } from "react"

interface FormFieldProps {
  label: string
  children: ReactNode
  error?: string
  required?: boolean
  className?: string
}

export default function FormField({ 
  label, 
  children, 
  error, 
  required = false, 
  className = "" 
}: FormFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-300">
        {label}
        {required && <span className="text-red-400 mr-1">*</span>}
      </label>
      {children}
      {error && (
        <p className="text-red-400 text-sm">{error}</p>
      )}
    </div>
  )
}

// Input component with consistent styling
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
}

export function Input({ error, className = "", ...props }: InputProps) {
  return (
    <input
      className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-200 ${
        error 
          ? "border-red-500/50 focus:ring-red-500/50" 
          : "border-gray-600/50 focus:border-purple-500/50 focus:ring-purple-500/50"
      } ${className}`}
      {...props}
    />
  )
}

// Textarea component with consistent styling
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean
}

export function Textarea({ error, className = "", ...props }: TextareaProps) {
  return (
    <textarea
      className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-200 resize-none ${
        error 
          ? "border-red-500/50 focus:ring-red-500/50" 
          : "border-gray-600/50 focus:border-purple-500/50 focus:ring-purple-500/50"
      } ${className}`}
      {...props}
    />
  )
}

// Select component with consistent styling
interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean
  options: Array<{ value: string; label: string }>
}

export function Select({ error, options, className = "", ...props }: SelectProps) {
  return (
    <select
      className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white focus:outline-none focus:ring-2 transition-all duration-200 ${
        error 
          ? "border-red-500/50 focus:ring-red-500/50" 
          : "border-gray-600/50 focus:border-purple-500/50 focus:ring-purple-500/50"
      } ${className}`}
      {...props}
    >
      {options.map((option) => (
        <option key={option.value} value={option.value} className="bg-gray-800">
          {option.label}
        </option>
      ))}
    </select>
  )
}

// Checkbox component with consistent styling
interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
}

export function Checkbox({ label, className = "", ...props }: CheckboxProps) {
  return (
    <label className={`flex items-center space-x-3 space-x-reverse cursor-pointer ${className}`}>
      <input
        type="checkbox"
        className="w-5 h-5 text-purple-600 bg-gray-700/50 border-gray-600/50 rounded focus:ring-purple-500/50 focus:ring-2"
        {...props}
      />
      <span className="text-gray-300">{label}</span>
    </label>
  )
}
