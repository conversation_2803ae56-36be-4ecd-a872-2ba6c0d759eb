"use client"

import { useState, useMemo } from "react"

interface UseSearchFilterOptions<T> {
  data: T[]
  searchFields: (keyof T)[]
  filterFunctions?: Record<string, (item: T, filterValue: string) => boolean>
}

export function useSearchFilter<T>({
  data,
  searchFields,
  filterFunctions = {}
}: UseSearchFilterOptions<T>) {
  const [searchValue, setSearchValue] = useState("")
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({})

  // Filter and search the data
  const filteredData = useMemo(() => {
    let result = [...data]

    // Apply search
    if (searchValue.trim()) {
      const searchTerm = searchValue.toLowerCase().trim()
      result = result.filter((item) =>
        searchFields.some((field) => {
          const value = item[field]
          if (typeof value === "string") {
            return value.toLowerCase().includes(searchTerm)
          }
          if (typeof value === "number") {
            return value.toString().includes(searchTerm)
          }
          return false
        })
      )
    }

    // Apply filters
    Object.entries(activeFilters).forEach(([filterKey, filterValue]) => {
      if (filterValue && filterFunctions[filterKey]) {
        result = result.filter((item) => filterFunctions[filterKey](item, filterValue))
      }
    })

    return result
  }, [data, searchValue, activeFilters, searchFields, filterFunctions])

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
  }

  // Handle filter change
  const handleFilterChange = (filterKey: string, value: string) => {
    setActiveFilters((prev) => ({
      ...prev,
      [filterKey]: value
    }))
  }

  // Clear all filters
  const clearFilters = () => {
    setActiveFilters({})
    setSearchValue("")
  }

  // Clear specific filter
  const clearFilter = (filterKey: string) => {
    setActiveFilters((prev) => {
      const newFilters = { ...prev }
      delete newFilters[filterKey]
      return newFilters
    })
  }

  // Get filter statistics
  const getFilterStats = (filterKey: string, getValue: (item: T) => string) => {
    const stats: Record<string, number> = {}
    
    data.forEach((item) => {
      const value = getValue(item)
      stats[value] = (stats[value] || 0) + 1
    })

    return Object.entries(stats).map(([value, count]) => ({
      value,
      count
    }))
  }

  return {
    searchValue,
    activeFilters,
    filteredData,
    handleSearchChange,
    handleFilterChange,
    clearFilters,
    clearFilter,
    getFilterStats,
    hasActiveFilters: Object.values(activeFilters).some(Boolean) || searchValue.trim() !== "",
    resultCount: filteredData.length,
    totalCount: data.length
  }
}
