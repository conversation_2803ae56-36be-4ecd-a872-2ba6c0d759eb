/**
 * Ultra-Lightweight Sync Check API
 * Minimizes database usage by only checking content hashes
 * Execution time: ~1-5ms, minimal data transfer
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createHash } from 'crypto'
import { z } from 'zod'

// Rate limiting for sync checks (more generous since it's lightweight)
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 30, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Input validation schema
const syncCheckSchema = z.object({
  hashes: z.record(z.string().min(1, 'Hash cannot be empty')), // Accept any non-empty hash
  tables: z.array(z.enum(['products', 'categories', 'currencies', 'banners', 'homepage_sections'])).optional()
})

// Generate content hash for a dataset
function generateContentHash(data: any[], tenantId: string, tableName: string): string {
  // Sort data by ID to ensure consistent hashing
  const sortedData = data.sort((a, b) => a.id.localeCompare(b.id))
  
  // Create deterministic content string
  const content = JSON.stringify(sortedData) + tenantId + tableName
  
  return createHash('sha256').update(content).digest('hex')
}

// Lightweight query to get only essential data for hashing
async function getTableHash(supabase: any, tableName: string, tenantId: string): Promise<string> {
  try {
    let query
    
    switch (tableName) {
      case 'products':
        // Only get fields that affect frontend display
        const { data } = await supabase
          .from('products')
          .select('id, title, slug, cover_image, category_id, featured, original_price, user_price, discount_price, distributor_price, updated_at')
          .eq('tenant_id', tenantId)
          .order('id')
        
        return generateContentHash(data || [], tenantId, tableName)
        
      case 'categories':
        const { data: categories } = await supabase
          .from('categories')
          .select('id, name, slug, image, updated_at')
          .eq('tenant_id', tenantId)
          .order('id')
        
        return generateContentHash(categories || [], tenantId, tableName)
        
      case 'currencies':
        const { data: currencies } = await supabase
          .from('currencies')
          .select('code, name, exchange_rate, is_active, updated_at')
          .eq('tenant_id', tenantId)
          .eq('is_active', true)
          .order('code')
        
        return generateContentHash(currencies || [], tenantId, tableName)
        
      case 'banners':
        const { data: banners } = await supabase
          .from('banner_slides')
          .select('id, title, image, link, order_index, is_active, updated_at')
          .eq('tenant_id', tenantId)
          .eq('is_active', true)
          .order('order_index')
        
        return generateContentHash(banners || [], tenantId, tableName)
        
      case 'homepage_sections':
        const { data: sections } = await supabase
          .from('homepage_sections')
          .select('id, title, type, content, order_index, is_active, updated_at')
          .eq('tenant_id', tenantId)
          .eq('is_active', true)
          .order('order_index')
        
        return generateContentHash(sections || [], tenantId, tableName)
        
      default:
        return ''
    }
  } catch (error) {
    console.error(`Error generating hash for ${tableName}:`, error)
    return ''
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 30)) { // 30 requests per minute
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    // Authentication check with Bearer token
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid authorization header' }, { status: 401 })
    }

    const token = authHeader.substring(7)

    // Create Supabase client with the provided token
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Parse and validate request
    const body = await request.json()
    console.log('📥 Sync API received body:', body)

    const validation = syncCheckSchema.safeParse(body)
    if (!validation.success) {
      console.error('❌ Validation failed:', validation.error.errors)
      return NextResponse.json({
        error: 'Validation error',
        details: validation.error.errors
      }, { status: 400 })
    }

    const { hashes, tables } = validation.data

    // Determine which tables to check
    const tablesToCheck = tables || Object.keys(hashes)
    
    // Generate current hashes for requested tables
    const currentHashes: Record<string, string> = {}
    const changed: string[] = []
    const unchanged: string[] = []

    // Process each table (this is very fast - just hash generation)
    for (const tableName of tablesToCheck) {
      const currentHash = await getTableHash(supabase, tableName, profile.tenant_id)
      currentHashes[tableName] = currentHash
      
      // Compare with client hash
      const clientHash = hashes[tableName]
      if (clientHash && clientHash === currentHash) {
        unchanged.push(tableName)
      } else {
        changed.push(tableName)
      }
    }

    // Return minimal response
    return NextResponse.json({
      changed,
      unchanged,
      hashes: currentHashes,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }
    
    console.error('Error in sync check:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET endpoint for simple health check
export async function GET() {
  return NextResponse.json({ 
    status: 'ok', 
    service: 'sync-check',
    timestamp: new Date().toISOString()
  })
}
