import { useState, useCallback, useEffect } from 'react'
import { useData } from '../../contexts/DataContext'
import type { Product } from '../../types'
import { PaginationData } from '../components/shared/AdminPagination'

interface UseUnifiedProductCRUDOptions {
  initialLimit?: number
}

interface FilterState {
  search?: string
  category?: string
  featured?: boolean
  popular?: boolean
  [key: string]: any
}

export function useUnifiedProductCRUD(options: UseUnifiedProductCRUDOptions = {}) {
  const { products: allProducts, addProduct, updateProduct, deleteProduct, isLoading } = useData()
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  
  const initialLimit = options.initialLimit || 10

  const createProduct = useCallback(async (productData: Partial<Product>) => {
    setActionLoading('create')
    try {
      const newProduct: Product = {
        id: Date.now().toString(),
        slug: productData.title?.toLowerCase().replace(/\s+/g, '-') || '',
        title: productData.title || '',
        description: productData.description || '',
        coverImage: productData.coverImage || '',
        category: productData.category || '',
        tags: productData.tags || [],
        rating: 0,
        commentCount: 0,
        packages: productData.packages || [],
        customFields: productData.customFields || [],
        dropdowns: productData.dropdowns || [],
        featured: productData.featured || false,
        popular: productData.popular || false,
        ...productData
      }
      
      const result = await addProduct(newProduct)
      return result
    } finally {
      setActionLoading(null)
    }
  }, [addProduct])

  const updateProductItem = useCallback(async (id: string, productData: Partial<Product>) => {
    setActionLoading(id)
    try {
      const existingProduct = allProducts.find(p => p.id === id)
      if (!existingProduct) {
        throw new Error('Product not found')
      }

      const updatedProduct: Product = {
        ...existingProduct,
        ...productData,
        id // Ensure ID doesn't change
      }
      
      const result = await updateProduct(updatedProduct)
      return result
    } finally {
      setActionLoading(null)
    }
  }, [allProducts, updateProduct])

  const deleteProductItem = useCallback(async (id: string) => {
    setActionLoading(id)
    try {
      const result = await deleteProduct(id)
      return result
    } finally {
      setActionLoading(null)
    }
  }, [deleteProduct])

  const getProduct = useCallback((id: string) => {
    return allProducts.find(p => p.id === id) || null
  }, [allProducts])

  const saveProduct = useCallback(async (productData: Partial<Product>) => {
    if (productData.id) {
      return updateProductItem(productData.id, productData)
    } else {
      return createProduct(productData)
    }
  }, [createProduct, updateProductItem])

  return {
    // State
    loading: isLoading,
    actionLoading,
    
    // Methods
    createProduct,
    updateProduct: updateProductItem,
    deleteProduct: deleteProductItem,
    getProduct,
    saveProduct
  }
}

export function useProductList(options: UseUnifiedProductCRUDOptions = {}) {
  const { products: allProducts, isLoading } = useData()
  const crud = useUnifiedProductCRUD(options)
  
  const [filters, setFilters] = useState<FilterState>({})
  const [currentPage, setCurrentPage] = useState(1)
  const [limit] = useState(options.initialLimit || 10)

  // Filter products based on current filters
  const filteredProducts = useCallback(() => {
    let filtered = [...allProducts]

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category)
    }

    // Featured filter
    if (filters.featured !== undefined && filters.featured !== '') {
      filtered = filtered.filter(product => product.featured === filters.featured)
    }

    // Popular filter
    if (filters.popular !== undefined && filters.popular !== '') {
      filtered = filtered.filter(product => product.popular === filters.popular)
    }

    return filtered
  }, [allProducts, filters])

  const products = filteredProducts()
  const totalProducts = products.length
  const totalPages = Math.ceil(totalProducts / limit)
  const startIndex = (currentPage - 1) * limit
  const endIndex = startIndex + limit
  const paginatedProducts = products.slice(startIndex, endIndex)

  const pagination: PaginationData = {
    page: currentPage,
    limit,
    total: totalProducts,
    totalPages,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1
  }

  const updateFilters = useCallback((newFilters: FilterState) => {
    setFilters(newFilters)
    setCurrentPage(1) // Reset to first page when filters change
  }, [])

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }, [totalPages])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      setCurrentPage(prev => prev + 1)
    }
  }, [pagination.hasNext])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      setCurrentPage(prev => prev - 1)
    }
  }, [pagination.hasPrev])

  const fetchProducts = useCallback(() => {
    // In a real app, this would trigger a data fetch
    // For now, we just use the products from context
  }, [])

  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  return {
    // State
    products: paginatedProducts,
    loading: isLoading,
    actionLoading: crud.actionLoading,
    pagination,
    filters,
    
    // Methods
    fetchProducts,
    updateFilters,
    goToPage,
    nextPage,
    prevPage,
    createItem: crud.createProduct,
    updateItem: crud.updateProduct,
    deleteItem: crud.deleteProduct
  }
}
