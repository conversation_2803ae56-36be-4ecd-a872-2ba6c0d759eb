"use client"

import React from 'react'
import { useAuth } from '../../contexts/AuthContext'
import LoginModal from './LoginModal'
import RegisterModal from './RegisterModal'
import ForgotPasswordModal from './ForgotPasswordModal'

export default function AuthModal() {
  const { currentModal, openModal, closeModal } = useAuth()

  const handleSwitchToLogin = () => {
    openModal('login')
  }

  const handleSwitchToRegister = () => {
    openModal('register')
  }

  const handleSwitchToForgotPassword = () => {
    openModal('forgot-password')
  }

  return (
    <>
      {/* Login Modal */}
      <LoginModal
        isOpen={currentModal === 'login'}
        onClose={closeModal}
        onSwitchToRegister={handleSwitchToRegister}
        onSwitchToForgotPassword={handleSwitchToForgotPassword}
      />

      {/* Register Modal */}
      <RegisterModal
        isOpen={currentModal === 'register'}
        onClose={closeModal}
        onSwitchToLogin={handleSwitchToLogin}
      />

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={currentModal === 'forgot-password'}
        onClose={closeModal}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </>
  )
}
