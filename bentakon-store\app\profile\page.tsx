"use client"

import { useState, useEffect } from "react"
import { User, Mail, Lock, Edit, Save, X, Camera, Wallet, Calendar, MapPin, Phone, LogOut } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { useData } from "../contexts/DataContext"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { UserBalances } from "../components/UserBalances"

export default function ProfilePage() {
  const { authState, logout } = useAuth()
  const { currencies, userBalances, refreshUserBalances } = useData()
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!authState.isAuthenticated && !authState.isLoading) {
      router.push('/')
    }
  }, [authState.isAuthenticated, authState.isLoading, router])

  const user = authState.user

  const [editForm, setEditForm] = useState({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      setEditForm(prev => ({
        ...prev,
        name: user.name,
        email: user.email,
        phone: user.phone || "",
      }))
    }
  }, [user])

  const handleSave = async () => {
    setIsSaving(true)

    // TODO: Implement user update with Supabase
    // Validate passwords match
    if (editForm.newPassword && editForm.newPassword !== editForm.confirmPassword) {
      toast.error("كلمات المرور غير متطابقة")
      setIsSaving(false)
      return
    }

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Update user data
    // TODO: Update user in context/database
    // For now, just show success message

    setIsEditing(false)
    setEditForm((prev) => ({
      ...prev,
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }))
    setIsSaving(false)
    toast.success("تم تحديث الملف الشخصي بنجاح!")
  }

  const handleCancel = () => {
    setEditForm({
      name: user?.name || "",
      email: user?.email || "",
      phone: user?.phone || "",
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    })
    setIsEditing(false)
  }

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true)
      await logout()
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    } catch (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    } finally {
      setIsLoggingOut(false)
    }
  }

  // Show loading while checking authentication
  if (authState.isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-400">جاري التحميل...</p>
          </div>
        </div>
      </div>
    )
  }

  // Don't render if no user (will be redirected)
  if (!user) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              الملف الشخصي
            </h1>
            <p className="text-gray-400 text-lg">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
          </div>

          {/* Logout Button - Only visible when authenticated */}
          {authState.isAuthenticated && (
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="flex items-center space-x-2 space-x-reverse bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300 px-4 py-2 rounded-lg font-medium transition-all duration-200 border border-red-600/30 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoggingOut ? (
                <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <LogOut className="w-4 h-4" />
              )}
              <span>{isLoggingOut ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج'}</span>
            </button>
          )}
        </div>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Profile Card */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">{user.name}</h2>
                  <p className="text-purple-100">عضو منذ {new Date(user.createdAt || Date.now()).getFullYear()}</p>
                </div>
              </div>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-white/20 hover:bg-white/30 text-white p-2 rounded-lg transition-colors"
                >
                  <Edit className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {!isEditing ? (
              /* View Mode */
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <User className="w-5 h-5 text-purple-400" />
                      <span className="text-sm text-gray-400">الاسم</span>
                    </div>
                    <p className="text-lg font-semibold">{user.name}</p>
                  </div>

                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <Mail className="w-5 h-5 text-blue-400" />
                      <span className="text-sm text-gray-400">البريد الإلكتروني</span>
                    </div>
                    <p className="text-lg font-semibold">{user.email}</p>
                  </div>
                </div>

                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center space-x-3 space-x-reverse mb-2">
                    <Lock className="w-5 h-5 text-green-400" />
                    <span className="text-sm text-gray-400">كلمة المرور</span>
                  </div>
                  <p className="text-lg">••••••••</p>
                </div>

                {/* Multi-Currency Balance Display */}
                <UserBalances />
              </div>
            ) : (
              /* Edit Mode */
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">الاسم</label>
                    <input
                      type="text"
                      value={editForm.name}
                      onChange={(e) => setEditForm((prev) => ({ ...prev, name: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm((prev) => ({ ...prev, email: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>

                <div className="border-t border-gray-700 pt-6">
                  <h3 className="text-lg font-semibold mb-4">تغيير كلمة المرور</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">كلمة المرور الحالية</label>
                      <input
                        type="password"
                        value={editForm.currentPassword}
                        onChange={(e) => setEditForm((prev) => ({ ...prev, currentPassword: e.target.value }))}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                        placeholder="أدخل كلمة المرور الحالية"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">كلمة المرور الجديدة</label>
                        <input
                          type="password"
                          value={editForm.newPassword}
                          onChange={(e) => setEditForm((prev) => ({ ...prev, newPassword: e.target.value }))}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                          placeholder="كلمة المرور الجديدة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">تأكيد كلمة المرور</label>
                        <input
                          type="password"
                          value={editForm.confirmPassword}
                          onChange={(e) => setEditForm((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                          placeholder="تأكيد كلمة المرور الجديدة"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700">
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="flex-1 btn-primary flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    {isSaving ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Save className="w-5 h-5" />
                        <span>حفظ التغييرات</span>
                      </>
                    )}
                  </button>
                  <button
                    onClick={handleCancel}
                    disabled={isSaving}
                    className="flex-1 btn-secondary flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    <X className="w-5 h-5" />
                    <span>إلغاء</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
