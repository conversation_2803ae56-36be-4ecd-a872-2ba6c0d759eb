import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user balances with currency info
    const { data: balances, error } = await supabase
      .from('user_currency_balances')
      .select(`
        currency_code,
        balance,
        currencies (
          name,
          exchange_rate
        )
      `)
      .eq('user_id', user.id)

    if (error) throw error

    return NextResponse.json({ balances: balances || [] })
  } catch (error) {
    console.error('Error fetching user balances:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { currency_code, amount, notes } = body

    if (!currency_code || !amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid currency code or amount' }, { status: 400 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Verify currency exists and is active
    const { data: currency } = await supabase
      .from('currencies')
      .select('code')
      .eq('code', currency_code)
      .eq('tenant_id', profile.tenant_id)
      .eq('is_active', true)
      .single()

    if (!currency) {
      return NextResponse.json({ error: 'Invalid or inactive currency' }, { status: 400 })
    }

    // Get current balance
    const { data: currentBalance } = await supabase
      .from('user_currency_balances')
      .select('balance')
      .eq('user_id', user.id)
      .eq('currency_code', currency_code)
      .single()

    const balanceBefore = currentBalance?.balance || 0
    const balanceAfter = balanceBefore + amount

    // Update or insert balance
    const { error: upsertError } = await supabase
      .from('user_currency_balances')
      .upsert({
        user_id: user.id,
        currency_code,
        balance: balanceAfter,
        tenant_id: profile.tenant_id,
        updated_at: new Date().toISOString()
      })

    if (upsertError) throw upsertError

    // Log the deposit
    const { error: logError } = await supabase
      .from('balance_change_log')
      .insert({
        user_id: user.id,
        currency_code,
        amount_change: amount,
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        change_type: 'deposit',
        notes: notes || 'User deposit',
        tenant_id: profile.tenant_id
      })

    if (logError) throw logError

    return NextResponse.json({ 
      success: true, 
      new_balance: balanceAfter,
      currency_code 
    })
  } catch (error) {
    console.error('Error adding balance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
