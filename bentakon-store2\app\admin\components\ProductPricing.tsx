"use client"

import { Al<PERSON>Circle, DollarSign, <PERSON>, Percent, TrendingUp } from "lucide-react"
import type { Product } from "../../types"
import FormField from "./FormField"

interface ProductPricingProps {
  formData: Partial<Product>
  onUpdate: (updates: Partial<Product>) => void
}

// Extended Product interface for future pricing features
interface ProductWithPricing extends Product {
  basePricing?: {
    originalPrice?: number
    userPrice?: number
    discountPrice?: number
    distributorPrice?: number
  }
}

export default function ProductPricing({
  formData,
  onUpdate,
}: ProductPricingProps) {
  // For now, we'll work with the concept of base pricing
  // In the current system, actual pricing is managed at package level
  const basePricing = (formData as ProductWithPricing).basePricing || {}

  const handlePricingChange = (field: string, value: number | undefined) => {
    const updatedPricing = {
      ...basePricing,
      [field]: value,
    }
    onUpdate({
      basePricing: updatedPricing,
    } as Partial<Product>)
  }

  return (
    <div className="space-y-6">
      {/* <PERSON>er */}
      <div className="border-b border-gray-700/50 pb-4">
        <h3 className="text-xl font-bold text-white">التسعير</h3>
        <p className="text-sm text-gray-400 mt-1">
          إعداد أسعار المنتج الأساسية (سيتم تطبيقها على الحزم)
        </p>
      </div>

      {/* Pricing Information Alert */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-blue-300">معلومات التسعير</h4>
            <div className="text-xs text-blue-200 space-y-1">
              <p>• <strong>السعر الأصلي:</strong> السعر الأساسي قبل أي خصومات</p>
              <p>• <strong>سعر المستخدم:</strong> السعر المعروض للمستخدمين العاديين</p>
              <p>• <strong>سعر الخصم:</strong> السعر المخفض للعروض الخاصة</p>
              <p>• <strong>سعر الموزع:</strong> السعر الخاص للموزعين والشركاء</p>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Original Price */}
        <FormField
          label="السعر الأصلي"
          required
          description="السعر الأساسي للمنتج قبل أي خصومات"
        >
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={basePricing.originalPrice || ""}
              onChange={(e) => handlePricingChange("originalPrice", Number(e.target.value) || undefined)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              placeholder="0.00"
            />
          </div>
        </FormField>

        {/* User Price */}
        <FormField
          label="سعر المستخدم"
          required
          description="السعر المعروض للمستخدمين العاديين"
        >
          <div className="relative">
            <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={basePricing.userPrice || ""}
              onChange={(e) => handlePricingChange("userPrice", Number(e.target.value) || undefined)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              placeholder="0.00"
            />
          </div>
        </FormField>

        {/* Discount Price */}
        <FormField
          label="سعر الخصم"
          description="السعر المخفض للعروض الخاصة (اختياري)"
        >
          <div className="relative">
            <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={basePricing.discountPrice || ""}
              onChange={(e) => handlePricingChange("discountPrice", Number(e.target.value) || undefined)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              placeholder="0.00"
            />
          </div>
        </FormField>

        {/* Distributor Price */}
        <FormField
          label="سعر الموزع"
          description="السعر الخاص للموزعين والشركاء (اختياري)"
        >
          <div className="relative">
            <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={basePricing.distributorPrice || ""}
              onChange={(e) => handlePricingChange("distributorPrice", Number(e.target.value) || undefined)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              placeholder="0.00"
            />
          </div>
        </FormField>
      </div>

      {/* Pricing Summary */}
      {(basePricing.originalPrice || basePricing.userPrice) && (
        <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
          <h4 className="text-sm font-semibold text-white mb-3">ملخص التسعير</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {basePricing.originalPrice && (
              <div className="text-center">
                <div className="text-gray-400">السعر الأصلي</div>
                <div className="text-white font-semibold">${basePricing.originalPrice.toFixed(2)}</div>
              </div>
            )}
            {basePricing.userPrice && (
              <div className="text-center">
                <div className="text-gray-400">سعر المستخدم</div>
                <div className="text-green-400 font-semibold">${basePricing.userPrice.toFixed(2)}</div>
              </div>
            )}
            {basePricing.discountPrice && (
              <div className="text-center">
                <div className="text-gray-400">سعر الخصم</div>
                <div className="text-orange-400 font-semibold">${basePricing.discountPrice.toFixed(2)}</div>
              </div>
            )}
            {basePricing.distributorPrice && (
              <div className="text-center">
                <div className="text-gray-400">سعر الموزع</div>
                <div className="text-blue-400 font-semibold">${basePricing.distributorPrice.toFixed(2)}</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Usage Guidelines */}
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-yellow-300 mb-2">إرشادات الاستخدام</h4>
        <div className="text-xs text-yellow-200 space-y-1">
          <p>• سيتم تطبيق هذه الأسعار كقيم افتراضية عند إنشاء حزم جديدة</p>
          <p>• يمكن تخصيص أسعار مختلفة لكل حزمة حسب الحاجة</p>
          <p>• سعر المستخدم هو السعر الأساسي المعروض في المتجر</p>
          <p>• أسعار الخصم والموزع تُستخدم في العروض الخاصة والشراكات</p>
        </div>
      </div>

      {/* Current System Note */}
      <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <AlertCircle className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-semibold text-purple-300 mb-1">ملاحظة النظام الحالي</h4>
            <p className="text-xs text-purple-200">
              في النظام الحالي، يتم إدارة التسعير على مستوى الحزم. هذا القسم يوفر إعدادات أساسية 
              للتسعير يمكن استخدامها كقيم افتراضية عند إنشاء حزم جديدة في القسم التالي.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
