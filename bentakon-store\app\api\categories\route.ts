import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 50, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// GET /api/categories - Get categories for current tenant
export async function GET(request: NextRequest) {
  try {
    console.log('Categories API called')
    
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 100)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    
    // First try to get tenant from headers (set by middleware)
    let tenantId = request.headers.get('x-tenant-id')
    console.log('Tenant ID from headers:', tenantId)
    
    // If no tenant in headers, try to get from user profile
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        console.log('User from auth:', user?.id)
        
        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('tenant_id')
            .eq('id', user.id)
            .single()
          
          tenantId = profile?.tenant_id
          console.log('Tenant ID from user profile:', tenantId)
        }
      } catch (authError) {
        console.log('No authenticated user, using main tenant:', authError)
      }
    }
    
    // If still no tenant, get main tenant as fallback
    if (!tenantId) {
      console.log('Getting main tenant as fallback')
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()
      
      tenantId = mainTenant?.id || 'caf1844f-4cc2-4c17-a775-1c837ae01051'
      console.log('Main tenant ID:', tenantId)
    }

    console.log('Fetching categories for tenant:', tenantId)
    
    // Get categories for this tenant (without is_active filter since column doesn't exist)
    const { data: categories, error } = await supabase
      .from('categories')
      .select(`
        id,
        name,
        slug,
        description,
        image,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .order('name', { ascending: true })

    console.log('Database query result:', { categories, error })

    if (error) {
      console.error('Database error fetching categories:', error)
      return NextResponse.json({ 
        success: false,
        error: 'Failed to fetch categories',
        details: error.message
      }, { status: 500 })
    }

    // Process categories (set is_active to true and product_count to 0 for now)
    const processedCategories = categories?.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      image: cat.image,
      is_active: true, // Default to true since column doesn't exist
      created_at: cat.created_at,
      updated_at: cat.updated_at,
      product_count: 0 // TODO: Add proper product count query later
    })) || []

    console.log('Processed categories:', processedCategories)

    return NextResponse.json({
      success: true,
      data: processedCategories,
      tenantId,
      count: processedCategories.length
    })

  } catch (error) {
    console.error('Error in GET /api/categories:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
