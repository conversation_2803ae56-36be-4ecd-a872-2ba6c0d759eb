import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Validation schemas
const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
  role: z.enum(['admin', 'distributor', 'user', 'worker']),
  phone: z.string().optional(),
  password: z.string().min(6).max(100)
})

const updateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  role: z.enum(['admin', 'distributor', 'user', 'worker']).optional(),
  phone: z.string().optional(),
  is_active: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 20)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get users for this tenant using service client to bypass RLS
    console.log('Admin Users API - Fetching users for tenant:', profile.tenant_id)
    console.log('Admin Users API - Current user profile:', profile)

    // Create service client for admin operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    const { data: users, error } = await serviceClient
      .from('user_profiles')
      .select(`
        id,
        name,
        role,
        phone,
        avatar,
        email,
        preferred_currency,
        wallet_balance,
        created_at,
        updated_at
      `)
      .eq('tenant_id', profile.tenant_id)
      .order('created_at', { ascending: false })

    console.log('Admin Users API - Raw query result:')
    console.log('- Users found:', users?.length || 0)
    console.log('- Error:', error?.message || 'none')
    console.log('- Users data:', JSON.stringify(users, null, 2))

    if (error) {
      console.error('Admin Users API - Database error:', error)
      throw error
    }

    // Get currency balances for all users
    const userIds = users?.map(u => u.id) || []
    const { data: balances } = await serviceClient
      .from('user_currency_balances')
      .select('user_id, currency_code, balance')
      .in('user_id', userIds)

    // Get currency balances for all users
    console.log('Admin Users API - Getting balances for user IDs:', userIds)

    // Combine user data with balances
    const usersWithBalances = users?.map(user => ({
      id: user.id,
      email: user.email || '',
      name: user.name,
      role: user.role,
      phone: user.phone,
      avatar: user.avatar,
      preferredCurrency: user.preferred_currency,
      walletBalance: Number(user.wallet_balance) || 0,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      currencyBalances: balances
        ?.filter(b => b.user_id === user.id)
        ?.map(b => ({
          currencyCode: b.currency_code,
          balance: Number(b.balance) || 0,
          currencyName: b.currencies?.name || b.currency_code,
          exchangeRate: Number(b.currencies?.exchange_rate) || 1
        })) || []
    })) || []

    console.log('Admin Users API - Final users with balances:', usersWithBalances.length)
    console.log('Admin Users API - Final result:', JSON.stringify(usersWithBalances.map(u => ({ id: u.id, name: u.name, role: u.role, email: u.email })), null, 2))

    const response = NextResponse.json({ users: usersWithBalances })

    // Add cache-busting headers to ensure fresh data
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')

    return response
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Check if user already exists in this tenant
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('email', validatedData.email)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (existingProfile) {
      return NextResponse.json({ error: 'User already exists in this tenant' }, { status: 409 })
    }

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: validatedData.email,
      password: validatedData.password,
      user_metadata: {
        name: validatedData.name,
        tenant_id: profile.tenant_id
      }
    })

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 })
    }

    // Create user profile
    const { data: newUser, error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        name: validatedData.name,
        role: validatedData.role,
        phone: validatedData.phone,
        tenant_id: profile.tenant_id,
        email: validatedData.email
      })
      .select()
      .single()

    if (profileError) throw profileError

    return NextResponse.json({ 
      user: {
        id: newUser.id,
        email: validatedData.email,
        name: newUser.name,
        role: newUser.role,
        phone: newUser.phone,
        createdAt: newUser.created_at
      }
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
