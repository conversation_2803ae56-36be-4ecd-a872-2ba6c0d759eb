# 🛍️ Bentakon Store - Multi-Tenant Digital Products Marketplace

A modern, secure, and scalable multi-tenant digital products marketplace built with Next.js 14, TypeScript, and Supabase. Perfect for creating multiple gaming storefronts with complete tenant isolation and custom branding.

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/altyb-apps/v0-bentakon-store)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.dev-black?style=for-the-badge)](https://v0.dev/chat/projects/T7OTftZkIO2)

## ✨ Features

### 🎯 Core Functionality
- **Multi-Tenant Architecture** - Complete tenant isolation with shared infrastructure
- **Digital Product Marketplace** - Browse and purchase digital products with instant delivery
- **Multi-Package Products** - Products can have multiple packages with different pricing
- **Secure Digital Code Delivery** - Encrypted digital codes with view tracking
- **User Wallet System** - Built-in wallet for seamless transactions
- **Tenant-Aware Admin Dashboard** - Comprehensive admin panel with tenant-scoped management
- **Dynamic Homepage Management** - Customizable banners and sections per tenant
- **Custom Branding** - Per-tenant themes, logos, and styling
- **Domain Management** - Support for custom domains and subdomains

### 🔐 Security & Authentication
- **Supabase Authentication** - Secure user authentication and authorization
- **Multi-Tenant Row Level Security (RLS)** - Complete data isolation between tenants
- **Tenant-Aware Policies** - Database-level security with tenant filtering
- **Input Validation** - Comprehensive validation using Zod schemas
- **Data Sanitization** - XSS protection and input sanitization
- **Super Admin Controls** - Cross-tenant management for platform administrators

### 🚀 Performance & UX
- **Code Splitting** - Lazy loading for optimal performance
- **Performance Monitoring** - Built-in performance tracking and Core Web Vitals
- **Loading States** - Comprehensive loading states and skeletons
- **Error Handling** - User-friendly error messages and recovery
- **Responsive Design** - Mobile-first responsive design
- **Accessibility** - WCAG compliant with screen reader support

### 🛠️ Developer Experience
- **TypeScript** - Full type safety throughout the application
- **ESLint & Prettier** - Code quality and formatting
- **Comprehensive Documentation** - Detailed guides and API documentation
