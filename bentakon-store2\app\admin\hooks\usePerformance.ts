"use client"

import { useMemo, useCallback, useRef, useState, useEffect } from "react"

// Hook for memoizing expensive calculations
export function useMemoizedCalculation<T, R>(
  data: T[],
  calculator: (data: T[]) => R,
  dependencies: any[] = []
): R {
  return useMemo(() => {
    return calculator(data)
  }, [data, ...dependencies])
}

// Hook for debouncing values (useful for search inputs)
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Hook for throttling function calls
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef<number>(0)
  const timeoutRef = useRef<NodeJS.Timeout>()

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now()
      
      if (now - lastCall.current >= delay) {
        lastCall.current = now
        return callback(...args)
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCall.current = Date.now()
          callback(...args)
        }, delay - (now - lastCall.current))
      }
    }) as T,
    [callback, delay]
  )
}

// Hook for optimizing re-renders with shallow comparison
export function useShallowMemo<T extends Record<string, any>>(obj: T): T {
  return useMemo(() => obj, Object.values(obj))
}

// Hook for batch state updates
export function useBatchedUpdates() {
  const [updates, setUpdates] = useState<(() => void)[]>([])
  const timeoutRef = useRef<NodeJS.Timeout>()

  const batchUpdate = useCallback((updateFn: () => void) => {
    setUpdates(prev => [...prev, updateFn])
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setUpdates(currentUpdates => {
        currentUpdates.forEach(fn => fn())
        return []
      })
    }, 0)
  }, [])

  return batchUpdate
}

// Hook for measuring component performance
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0)
  const startTime = useRef<number>(0)

  renderCount.current++

  useEffect(() => {
    startTime.current = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime.current
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`)
      }
    }
  })

  return {
    renderCount: renderCount.current,
    logPerformance: (operation: string, startTime: number) => {
      const endTime = performance.now()
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} ${operation}: ${(endTime - startTime).toFixed(2)}ms`)
      }
    }
  }
}
