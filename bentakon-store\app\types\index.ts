/**
 * Category Entity - Represents product categories with tenant isolation
 *
 * Database Table: categories
 * Primary Key: id
 * Indexes: tenant_id, slug, is_active, sort_order
 *
 * Relationships:
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with Product (category_id)
 */
export interface Category {
  id: string              // UUID - Primary key, auto-generated
  tenant_id: string       // UUID - Tenant identifier for multi-tenant support
  name?: string           // Optional display name of the category
  slug: string            // URL-friendly identifier, unique per tenant (required)
  description?: string    // Optional description for the category
  image: string           // Image URL for the category (required)
  created_at: string      // ISO timestamp of creation
  updated_at: string      // ISO timestamp of last update
  product_count?: number  // Virtual field - number of products in category
}

/**
 * Product Entity - Represents a game or digital service available for purchase
 *
 * Database Table: products
 * Primary Key: id
 * Indexes: slug (unique), category, category_id, featured, popular
 *
 * Relationships:
 * - Many-to-One with Category (category_id)
 * - One-to-Many with Package (packages)
 * - One-to-Many with CustomField (customFields)
 * - One-to-Many with Dropdown (dropdowns)
 * - Referenced by Order (productId)
 * - Referenced by HomepageSection (productIds array)
 */
export interface Product {
  id: string              // UUID - Primary key, auto-generated
  tenant_id?: string      // UUID - Tenant identifier for multi-tenant support
  slug: string            // URL-friendly identifier, unique per tenant
  title: string           // Display name of the product
  description: string     // Detailed description for product page
  coverImage: string      // URL to product cover image
  category_id?: string    // UUID - Foreign key to categories table
  categoryData?: Category // Virtual field - populated category data
  tags: string[]          // Array of tags for filtering and search
  rating: number          // Average user rating (0-5)
  commentCount: number    // Number of user reviews/comments
  packages: Package[]     // Available purchase packages for this product
  customFields?: CustomField[]  // Custom input fields required for purchase
  dropdowns?: Dropdown[]  // Dropdown selection fields for purchase
  featured: boolean       // Whether product appears in featured sections
  // Product-level pricing (used when no packages exist)
  original_price?: number // Backend cost calculation, never shown to users
  user_price?: number     // Public selling price shown to regular users
  discount_price?: number // Discounted price for regular users (reduction from user_price)
  distributor_price?: number // Special price for distributor role users
}

/**
 * Package Entity - Represents different purchase options for a product
 *
 * Database Table: packages
 * Primary Key: id
 * Foreign Key: productId (references products.id)
 * Indexes: productId, hasDigitalCodes
 *
 * Relationships:
 * - Many-to-One with Product (productId)
 * - One-to-Many with DigitalCode (digitalCodes)
 * - Referenced by Order (packageId)
 */
export interface Package {
  id: string                    // UUID - Primary key
  name: string                  // Package name (e.g., "60 Diamonds")
  // Legacy price field for backward compatibility
  price?: number                // Legacy: Current selling price in USD
  // Enhanced pricing fields
  original_price: number        // Backend cost calculation, never shown to users
  user_price: number            // Public selling price shown to regular users
  discount_price?: number       // Discounted price for regular users (reduction from user_price)
  distributor_price?: number    // Special price for distributor role users
  image: string                 // URL to package image or reference
  use_product_image?: boolean   // Whether to use parent product's image
  image_reference_type?: 'url' | 'product_image'  // Type of image reference
  description?: string          // Optional package description
  digitalCodes?: DigitalCode[]  // Associated digital codes for instant delivery
  hasDigitalCodes?: boolean     // Whether this package uses digital codes
  availableCodesCount?: number  // Number of unused codes available
  // Manual inventory management fields
  manual_quantity?: number      // Manual inventory quantity for non-digital packages
  track_inventory?: boolean     // Whether to track inventory for this package
  unlimited_stock?: boolean     // Whether this package has unlimited stock
}

/**
 * DigitalCode Entity - Represents redeemable codes for digital products
 *
 * Database Table: digital_codes
 * Primary Key: id
 * Foreign Key: packageId (references packages.id)
 * Indexes: packageId, used, assignedToOrderId
 *
 * Relationships:
 * - Many-to-One with Package (packageId)
 * - One-to-One with Order (assignedToOrderId)
 *
 * Security: Keys should be encrypted at rest
 */
export interface DigitalCode {
  id: string                      // UUID - Primary key
  key: string                     // The actual redeemable code (encrypted)
  used: boolean                   // Whether code has been assigned to an order
  assignedToOrderId: string | null // Order this code is assigned to
  assignedAt?: string             // Timestamp when code was assigned
  viewedCount?: number            // How many times customer viewed the code
  lastViewedAt?: string           // Last time customer viewed the code
}

/**
 * CustomField Entity - Defines custom input fields required for product/package purchase
 *
 * Database Table: custom_fields
 * Primary Key: id
 * Foreign Key: productId (references products.id)
 * Indexes: product_id, package_id, field_order
 *
 * Used for collecting user-specific data like Player ID, Server ID, etc.
 * Supports both product-level and package-level fields with enhanced validation.
 */
export interface CustomField {
  id: string                              // UUID - Primary key
  tenant_id?: string                      // UUID - Tenant identifier
  product_id?: string                     // UUID - Foreign key to products (for product-level fields)
  package_id?: string                     // UUID - Foreign key to packages (for package-level fields)
  label: string                           // Field label displayed to user
  field_type: "text" | "email" | "password" | "number" | "tel" | "url" | "textarea" | "select" | "radio" | "checkbox" // Enhanced field types
  description?: string                    // Rich description explaining the field purpose
  required: boolean                       // Whether field is mandatory
  placeholder?: string                    // Placeholder text for input
  field_order: number                     // Display order for multiple fields
  validation_rules?: {                    // JSON validation rules
    pattern?: string                      // Regex pattern for validation
    minLength?: number                    // Minimum length
    maxLength?: number                    // Maximum length
    min?: number                          // Minimum value (for numbers)
    max?: number                          // Maximum value (for numbers)
  }
  display_options?: {                     // JSON display options
    icon?: string                         // Icon name to display
    helpText?: string                     // Additional help text
    showStrength?: boolean                // Show password strength (for password fields)
    rows?: number                         // Number of rows (for textarea)
  }
  // Legacy support for backward compatibility
  type?: "text" | "email" | "number"     // Backward compatibility field
}

/**
 * Dropdown Entity - Defines dropdown selection fields for product/package purchase
 *
 * Database Table: dropdowns
 * Primary Key: id
 * Foreign Key: product_id (references products.id) OR package_id (references packages.id)
 * Indexes: product_id, package_id, field_order
 */
export interface Dropdown {
  id: string                    // UUID - Primary key
  tenant_id?: string            // UUID - Tenant identifier
  product_id?: string           // UUID - Foreign key to products (for product-level dropdowns)
  package_id?: string           // UUID - Foreign key to packages (for package-level dropdowns)
  label: string                 // Dropdown label displayed to user
  description?: string          // Rich description explaining the dropdown purpose
  options: DropdownOption[]     // Available options for selection
  required: boolean             // Whether selection is mandatory
  field_order: number           // Display order for multiple fields
  validation_rules?: {          // JSON validation rules
    allowMultiple?: boolean     // Allow multiple selections
    minSelections?: number      // Minimum number of selections
    maxSelections?: number      // Maximum number of selections
  }
  display_options?: {           // JSON display options
    icon?: string               // Icon name to display
    helpText?: string           // Additional help text
    searchable?: boolean        // Enable search in dropdown
    placeholder?: string        // Placeholder text
  }
}

/**
 * DropdownOption Entity - Individual options within a dropdown
 *
 * Database Table: dropdown_options
 * Primary Key: id
 * Foreign Key: field_id (references dropdowns.id)
 * Indexes: field_id, display_order
 */
export interface DropdownOption {
  id: string              // UUID - Primary key
  tenant_id?: string      // UUID - Tenant identifier
  field_id: string        // UUID - Foreign key to dropdowns table
  label: string           // Option label displayed to user (also used as value)
  description?: string    // Optional description for the option
  display_order: number   // Display order within the dropdown
  is_default: boolean     // Whether this is the default selected option
}

/**
 * User Entity - Represents system users with role-based access
 *
 * Database Table: users (managed by Supabase Auth)
 * Primary Key: id (UUID from Supabase Auth)
 * Indexes: email (unique), role
 *
 * Relationships:
 * - One-to-Many with Order (userId)
 *
 * Security: Integrated with Supabase Auth for authentication
 * Row Level Security (RLS) policies required for data protection
 */
export interface User {
  id: string                                    // UUID from Supabase Auth
  tenant_id?: string                            // UUID - Tenant identifier for multi-tenant support
  email: string                                 // User email (unique, from Auth)
  name: string                                  // Display name
  role: "admin" | "distributor" | "user" | "worker"  // User role for access control
  walletBalance: number                         // User's wallet balance in USD
  avatar?: string                               // Optional profile picture URL
  phone?: string                                // Optional phone number
  createdAt?: string                            // Account creation date
}

/**
 * Order Entity - Represents customer purchase transactions
 *
 * Database Table: orders
 * Primary Key: id
 * Foreign Keys:
 *   - user_id (references user_profiles.id)
 *   - product_id (references products.id)
 *   - package_id (references packages.id)
 *   - tenant_id (references tenants.id)
 * Indexes: (tenant_id, user_id), (tenant_id, product_id), (tenant_id, status), (tenant_id, created_at)
 *
 * Relationships:
 * - Many-to-One with User (user_id)
 * - Many-to-One with Product (product_id)
 * - Many-to-One with Package (package_id)
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with DigitalCode (via assigned_to_order_id)
 *
 * Security: Users can only access their own orders within their tenant (RLS policy)
 */
export interface Order {
  id: string                                    // UUID - Primary key
  tenant_id: string                             // UUID - Tenant identifier for multi-tenant support
  user_id: string                               // Customer who placed the order
  product_id: string                            // Product being purchased
  package_id: string                            // Specific package/variant
  amount: number                                // Total amount paid in USD
  status: "pending" | "completed" | "failed"   // Order processing status
  custom_data?: Record<string, any>             // Custom field data (Player ID, etc.)
  created_at: string                            // ISO timestamp of order creation
  updated_at: string                            // ISO timestamp of last update

  // Worker tracking fields
  worker_id?: string                            // UUID - Worker who processed the order
  worker_action?: "accepted" | "rejected"       // Action taken by worker
  worker_action_at?: string                     // ISO timestamp of worker action

  // Joined data from related tables (when using SELECT with joins)
  products?: {
    id: string
    title: string
    slug: string
    coverImage: string
  }
  packages?: {
    id: string
    name: string
    price: number
    image: string
  }
  user_profiles?: {
    id: string
    name: string
    email: string
  }
  worker_profiles?: {
    id: string
    name: string
    email: string
    role: string
  }
  digitalCodes?: {
    id: string
    key_encrypted: string
    used: boolean
    viewed_count: number
    last_viewed_at: string
  }[]
}

/**
 * BannerSlide Entity - Represents promotional banners on homepage
 *
 * Database Table: banner_slides
 * Primary Key: id
 * Indexes: active, order
 *
 * Used for homepage carousel/banner management
 */
export interface BannerSlide {
  id: string                                          // UUID - Primary key
  tenant_id?: string                                  // UUID - Tenant identifier for multi-tenant support
  title: string                                       // Banner title text
  subtitle?: string                                   // Optional subtitle text
  image: string                                       // Banner image URL
  linkType: "product" | "collection" | "custom" | "none"  // Type of link action
  linkValue?: string                                  // Link target (product slug, URL, etc.)
  active: boolean                                     // Whether banner is currently shown
  order: number                                       // Display order (lower = first)
}

/**
 * HomepageSection Entity - Represents product sections on homepage
 *
 * Database Table: homepage_sections
 * Primary Key: id
 * Indexes: active, order
 *
 * Relationships:
 * - References Product (productIds array)
 *
 * Used for organizing products into themed sections
 */
export interface HomepageSection {
  id: string              // UUID - Primary key
  tenant_id?: string      // UUID - Tenant identifier for multi-tenant support
  title: string           // Section title (e.g., "🔥 Popular Games") - can include emojis directly
  productIds: string[]    // Array of product IDs to display
  order: number           // Display order on homepage
  active: boolean         // Whether section is currently shown
}

/**
 * HomepageConfig - Configuration object for homepage layout
 *
 * Not a database table - used for API responses and admin management
 */
export interface HomepageConfig {
  banners: BannerSlide[]      // Active banner slides
  sections: HomepageSection[] // Active homepage sections
}
