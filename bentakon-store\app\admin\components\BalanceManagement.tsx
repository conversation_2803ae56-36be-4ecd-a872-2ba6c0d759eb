"use client"

import { useState, useEffect } from "react"
import { X, Plus, Minus, DollarSign, History, AlertCircle } from "lucide-react"
import { toast } from "sonner"

interface Currency {
  code: string
  name: string
  exchange_rate: number
}

interface UserBalance {
  currencyCode: string
  balance: number
  currencyName: string
  exchangeRate: number
  isActive: boolean
  updatedAt: string
}

interface BalanceManagementProps {
  userId: string
  userName: string
  isOpen: boolean
  onClose: () => void
}

export default function BalanceManagement({ userId, userName, isOpen, onClose }: BalanceManagementProps) {
  const [balances, setBalances] = useState<UserBalance[]>([])
  const [availableCurrencies, setAvailableCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedCurrency, setSelectedCurrency] = useState('')
  const [amount, setAmount] = useState('')
  const [operation, setOperation] = useState<'add' | 'subtract' | 'set'>('add')
  const [notes, setNotes] = useState('')
  const [error, setError] = useState('')

  // Load user balances and available currencies
  const loadBalances = async () => {
    if (!userId) return
    
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}/balances`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch balances')
      }
      
      const data = await response.json()
      setBalances(data.balances || [])
      setAvailableCurrencies(data.availableCurrencies || [])
      
      // Set default currency to first available if none selected
      if (!selectedCurrency && data.availableCurrencies?.length > 0) {
        setSelectedCurrency(data.availableCurrencies[0].code)
      }
    } catch (error) {
      console.error('Error loading balances:', error)
      toast.error('فشل في تحميل الأرصدة')
    } finally {
      setLoading(false)
    }
  }

  // Update user balance
  const updateBalance = async () => {
    const numericAmount = parseFloat(amount)

    if (!selectedCurrency || !amount || numericAmount <= 0) {
      setError('يرجى اختيار العملة وإدخال مبلغ صحيح')
      return
    }

    // Validate maximum amount (999 billion with 2 decimal places)
    if (numericAmount > 999999999999.99) {
      setError('المبلغ كبير جداً. الحد الأقصى هو 999,999,999,999.99')
      return
    }

    try {
      setLoading(true)
      setError('')

      const response = await fetch(`/api/admin/users/${userId}/balances`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currency_code: selectedCurrency,
          amount: numericAmount,
          operation,
          notes: notes || `Admin ${operation} operation`
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(`تم ${operation === 'add' ? 'إضافة' : operation === 'subtract' ? 'خصم' : 'تعديل'} الرصيد بنجاح`)
        await loadBalances() // Reload balances

        // Reset form
        setAmount('')
        setNotes('')
      } else {
        setError(data.error || 'فشل في تحديث الرصيد')
      }
    } catch (error) {
      console.error('Error updating balance:', error)
      setError('حدث خطأ في تحديث الرصيد')
    } finally {
      setLoading(false)
    }
  }

  // Load balances when modal opens
  useEffect(() => {
    if (isOpen && userId) {
      loadBalances()
    }
  }, [isOpen, userId])

  if (!isOpen) return null

  const selectedCurrencyData = availableCurrencies.find(c => c.code === selectedCurrency)
  const currentBalance = balances.find(b => b.currencyCode === selectedCurrency)?.balance || 0

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div>
            <h2 className="text-2xl font-bold text-white">إدارة الأرصدة</h2>
            <p className="text-gray-400 mt-1">المستخدم: {userName}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Current Balances */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              الأرصدة الحالية
            </h3>
            
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
              </div>
            ) : balances.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {balances.map((balance) => (
                  <div key={balance.currencyCode} className="bg-gray-700/50 rounded-lg p-4 border border-gray-600/50">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-gray-400 text-sm">{balance.currencyName}</p>
                        <p className="text-white font-bold text-lg">
                          {balance.balance.toFixed(2)} {balance.currencyCode}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-gray-500 text-xs">سعر الصرف</p>
                        <p className="text-gray-400 text-sm">{balance.exchangeRate}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد أرصدة</p>
            )}
          </div>

          {/* Balance Management Form */}
          <div className="bg-gray-700/30 rounded-lg p-6 border border-gray-600/30">
            <h3 className="text-lg font-semibold text-white mb-4">تعديل الرصيد</h3>
            
            <div className="space-y-4">
              {/* Currency Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">العملة</label>
                <select
                  value={selectedCurrency}
                  onChange={(e) => setSelectedCurrency(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  disabled={loading}
                >
                  <option value="">اختر العملة</option>
                  {availableCurrencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.code} - {currency.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Current Balance Display */}
              {selectedCurrency && (
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <p className="text-blue-400 text-sm">
                    الرصيد الحالي: <span className="font-bold">{currentBalance.toFixed(2)} {selectedCurrency}</span>
                  </p>
                  {selectedCurrencyData && (
                    <p className="text-gray-400 text-xs mt-1">
                      سعر الصرف: {selectedCurrencyData.exchange_rate} (من USD)
                    </p>
                  )}
                </div>
              )}

              {/* Operation Type */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">نوع العملية</label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    type="button"
                    onClick={() => setOperation('add')}
                    className={`flex items-center justify-center px-3 py-2 rounded-lg border transition-colors ${
                      operation === 'add'
                        ? 'bg-green-600 border-green-500 text-white'
                        : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    إضافة
                  </button>
                  <button
                    type="button"
                    onClick={() => setOperation('subtract')}
                    className={`flex items-center justify-center px-3 py-2 rounded-lg border transition-colors ${
                      operation === 'subtract'
                        ? 'bg-red-600 border-red-500 text-white'
                        : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <Minus className="w-4 h-4 mr-1" />
                    خصم
                  </button>
                  <button
                    type="button"
                    onClick={() => setOperation('set')}
                    className={`flex items-center justify-center px-3 py-2 rounded-lg border transition-colors ${
                      operation === 'set'
                        ? 'bg-blue-600 border-blue-500 text-white'
                        : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    <DollarSign className="w-4 h-4 mr-1" />
                    تعيين
                  </button>
                </div>
              </div>

              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">المبلغ</label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  placeholder="أدخل المبلغ"
                  min="0"
                  step="0.01"
                  disabled={loading}
                />
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">ملاحظات (اختياري)</label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  placeholder="أدخل ملاحظات حول العملية"
                  rows={3}
                  disabled={loading}
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center space-x-2 space-x-reverse text-red-400 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <AlertCircle className="w-5 h-5" />
                  <span>{error}</span>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3 space-x-reverse pt-4">
                <button
                  onClick={updateBalance}
                  disabled={loading || !selectedCurrency || !amount}
                  className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      {operation === 'add' ? <Plus className="w-5 h-5 mr-2" /> : 
                       operation === 'subtract' ? <Minus className="w-5 h-5 mr-2" /> : 
                       <DollarSign className="w-5 h-5 mr-2" />}
                      {operation === 'add' ? 'إضافة رصيد' : 
                       operation === 'subtract' ? 'خصم رصيد' : 
                       'تعيين رصيد'}
                    </>
                  )}
                </button>
                <button
                  onClick={onClose}
                  className="px-6 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 rounded-lg transition-colors"
                  disabled={loading}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
