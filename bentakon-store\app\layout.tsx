import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"
import Header from "./components/Header"
import Footer from "./components/Footer"
import { DataProvider } from "./contexts/DataContext"
import { AuthProvider } from "./contexts/AuthContext"
import { TenantProvider } from "./contexts/TenantContext"
import TenantTheme from "./components/TenantTheme"
import AuthModal from "./components/auth/AuthModal"
import { ToastProvider } from "./components/Toast"
import { SkipToMain } from "./components/Accessibility"
import { Toaster } from "@/components/ui/sonner"
import CacheStats from "./components/CacheStats"

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
})

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'),
  title: "متجر بنتاكون - خدمات الألعاب والمنتجات الرقمية",
  description: "متجرك الموثوق لشحن الألعاب والحسابات والخدمات الرقمية",
  keywords: "ألعاب, شحن ألعاب, منتجات رقمية, خدمات ألعاب, بنتاكون",
  generator: 'v0.dev',
  icons: {
    icon: '/logo.jpg',
    shortcut: '/logo.jpg',
    apple: '/logo.jpg',
  },
  openGraph: {
    title: "متجر بنتاكون - خدمات الألعاب والمنتجات الرقمية",
    description: "متجرك الموثوق لشحن الألعاب والحسابات والخدمات الرقمية",
    images: ['/logo.jpg'],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "متجر بنتاكون - خدمات الألعاب والمنتجات الرقمية",
    description: "متجرك الموثوق لشحن الألعاب والحسابات والخدمات الرقمية",
    images: ['/logo.jpg'],
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="dark">
      <body className={`${cairo.className} bg-gray-900 text-white min-h-screen`}>
        <SkipToMain />
        <TenantProvider>
          <TenantTheme>
            <DataProvider>
              <AuthProvider>
                <Header />
                <main id="main-content" className="min-h-screen" tabIndex={-1}>
                  {children}
                </main>
                <Footer />
                <AuthModal />
                {process.env.NODE_ENV === 'development' && <CacheStats />}
              </AuthProvider>
            </DataProvider>
          </TenantTheme>
        </TenantProvider>
        <ToastProvider />
        <Toaster />
      </body>
    </html>
  )
}
