import React from 'react'
import { Eye, Edit, Trash2 } from 'lucide-react'

export interface ColumnDefinition<T> {
  key: keyof T | string
  label: string
  render?: (item: T) => React.ReactNode
  className?: string
  mobileHidden?: boolean
}

export interface AdminTableProps<T extends { id: string }> {
  items: T[]
  columns: ColumnDefinition<T>[]
  loading: boolean
  actionLoading: string | null
  onView?: (item: T) => void
  onEdit?: (item: T) => void
  onDelete?: (item: T) => void
  renderMobileCard: (item: T) => React.ReactNode
  emptyState?: {
    icon: React.ReactNode
    title: string
    description: string
    action?: React.ReactNode
  }
}

export default function AdminTable<T extends { id: string }>({
  items,
  columns,
  loading,
  actionLoading,
  onView,
  onEdit,
  onDelete,
  renderMobileCard,
  emptyState
}: AdminTableProps<T>) {
  if (loading) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-16 bg-gray-700/50 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (items.length === 0 && emptyState) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        <div className="p-12 text-center">
          {emptyState.icon}
          <h3 className="text-xl font-semibold text-white mt-4 mb-2">
            {emptyState.title}
          </h3>
          <p className="text-gray-400 mb-6">
            {emptyState.description}
          </p>
          {emptyState.action}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700/50">
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`text-right p-4 text-sm font-medium text-gray-300 ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
              {(onView || onEdit || onDelete) && (
                <th className="text-right p-4 text-sm font-medium text-gray-300">
                  الإجراءات
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr
                key={item.id}
                className="border-b border-gray-700/30 hover:bg-gray-700/20 transition-colors"
              >
                {columns.map((column) => (
                  <td
                    key={String(column.key)}
                    className={`p-4 text-sm text-gray-300 ${column.className || ''}`}
                  >
                    {column.render ? column.render(item) : String(item[column.key as keyof T] || '')}
                  </td>
                ))}
                {(onView || onEdit || onDelete) && (
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      {onView && (
                        <button
                          onClick={() => onView(item)}
                          className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-400/10 rounded-lg transition-all duration-300"
                          title="عرض"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}
                      {onEdit && (
                        <button
                          onClick={() => onEdit(item)}
                          disabled={actionLoading === item.id}
                          className="p-2 text-gray-400 hover:text-purple-400 hover:bg-purple-400/10 rounded-lg transition-all duration-300 disabled:opacity-50"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      )}
                      {onDelete && (
                        <button
                          onClick={() => onDelete(item)}
                          disabled={actionLoading === item.id}
                          className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-all duration-300 disabled:opacity-50"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden p-4 space-y-4">
        {items.map((item) => renderMobileCard(item))}
      </div>
    </div>
  )
}
