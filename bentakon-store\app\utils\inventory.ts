/**
 * Inventory management utilities for packages
 * Handles both digital codes and manual inventory tracking
 */

import type { Package } from '../types'

export interface InventoryStatus {
  available: number
  isAvailable: boolean
  isUnlimited: boolean
  type: 'digital' | 'manual' | 'none'
  lowStock: boolean
  outOfStock: boolean
}

/**
 * Calculate the available quantity for a package
 * Handles both digital codes and manual inventory
 */
export function getPackageAvailableQuantity(pkg: Package): number {
  // If unlimited stock is enabled, return a large number for display purposes
  if (pkg.unlimited_stock) {
    return Number.MAX_SAFE_INTEGER
  }

  // If package uses digital codes, count available codes
  if (pkg.hasDigitalCodes || pkg.has_digital_codes) {
    return pkg.availableCodesCount || 0
  }

  // If package tracks manual inventory, return manual quantity
  if (pkg.track_inventory) {
    return pkg.manual_quantity || 0
  }

  // Default case: no inventory tracking, assume unlimited
  return Number.MAX_SAFE_INTEGER
}

/**
 * Get comprehensive inventory status for a package
 */
export function getPackageInventoryStatus(pkg: Package): InventoryStatus {
  const available = getPackageAvailableQuantity(pkg)
  const isUnlimited = pkg.unlimited_stock || (!pkg.track_inventory && !(pkg.hasDigitalCodes || pkg.has_digital_codes))
  
  let type: 'digital' | 'manual' | 'none' = 'none'
  if (pkg.hasDigitalCodes || pkg.has_digital_codes) {
    type = 'digital'
  } else if (pkg.track_inventory) {
    type = 'manual'
  }

  return {
    available,
    isAvailable: available > 0 || isUnlimited,
    isUnlimited,
    type,
    lowStock: !isUnlimited && available > 0 && available <= 5,
    outOfStock: !isUnlimited && available === 0
  }
}

/**
 * Check if a package is available for purchase with the requested quantity
 */
export function isPackageAvailable(pkg: Package, requestedQuantity: number = 1): boolean {
  const status = getPackageInventoryStatus(pkg)
  
  if (status.isUnlimited) {
    return true
  }
  
  return status.available >= requestedQuantity
}

/**
 * Get inventory display text for a package
 */
export function getInventoryDisplayText(pkg: Package): string {
  const status = getPackageInventoryStatus(pkg)
  
  if (status.isUnlimited) {
    return 'متوفر'
  }
  
  if (status.outOfStock) {
    return 'نفد المخزون'
  }
  
  if (status.type === 'digital') {
    return `متوفر (${status.available} كود)`
  }
  
  if (status.type === 'manual') {
    return `متوفر (${status.available} قطعة)`
  }
  
  return 'متوفر'
}

/**
 * Get inventory status color class for UI
 */
export function getInventoryStatusColor(pkg: Package): string {
  const status = getPackageInventoryStatus(pkg)
  
  if (status.outOfStock) {
    return 'text-red-400'
  }
  
  if (status.lowStock) {
    return 'text-orange-400'
  }
  
  return 'text-green-400'
}

/**
 * Calculate total available inventory across all packages of a product
 */
export function getProductTotalInventory(packages: Package[]): {
  totalAvailable: number
  hasUnlimited: boolean
  digitalCodes: number
  manualStock: number
} {
  let totalAvailable = 0
  let hasUnlimited = false
  let digitalCodes = 0
  let manualStock = 0

  for (const pkg of packages) {
    const status = getPackageInventoryStatus(pkg)

    if (status.isUnlimited) {
      hasUnlimited = true
    } else {
      totalAvailable += status.available
    }

    if (status.type === 'digital') {
      digitalCodes += status.available
    } else if (status.type === 'manual') {
      manualStock += status.available
    }
  }

  return {
    totalAvailable: hasUnlimited ? Number.MAX_SAFE_INTEGER : totalAvailable,
    hasUnlimited,
    digitalCodes,
    manualStock
  }
}

/**
 * Validate inventory before order placement
 */
export function validateInventoryForOrder(pkg: Package, quantity: number): {
  valid: boolean
  error?: string
} {
  if (!isPackageAvailable(pkg, quantity)) {
    const status = getPackageInventoryStatus(pkg)
    
    if (status.outOfStock) {
      return {
        valid: false,
        error: 'هذا المنتج غير متوفر حالياً'
      }
    }
    
    return {
      valid: false,
      error: `الكمية المطلوبة (${quantity}) أكبر من المتوفر (${status.available})`
    }
  }
  
  return { valid: true }
}

/**
 * Get inventory warning message for low stock
 */
export function getInventoryWarning(pkg: Package): string | null {
  const status = getPackageInventoryStatus(pkg)
  
  if (status.lowStock) {
    if (status.type === 'digital') {
      return `متبقي ${status.available} أكواد فقط في المخزون`
    } else if (status.type === 'manual') {
      return `متبقي ${status.available} قطع فقط في المخزون`
    }
  }
  
  return null
}

/**
 * Sort packages by availability (available first, then by quantity)
 */
export function sortPackagesByAvailability(packages: Package[]): Package[] {
  return [...packages].sort((a, b) => {
    const statusA = getPackageInventoryStatus(a)
    const statusB = getPackageInventoryStatus(b)
    
    // Available packages first
    if (statusA.isAvailable && !statusB.isAvailable) return -1
    if (!statusA.isAvailable && statusB.isAvailable) return 1
    
    // Unlimited stock first among available
    if (statusA.isUnlimited && !statusB.isUnlimited) return -1
    if (!statusA.isUnlimited && statusB.isUnlimited) return 1
    
    // Sort by available quantity (higher first)
    return statusB.available - statusA.available
  })
}

/**
 * Check if any package in a product is available
 */
export function hasAvailablePackages(packages: Package[]): boolean {
  return packages.some(pkg => isPackageAvailable(pkg))
}

/**
 * Get the most available package (for auto-selection)
 */
export function getMostAvailablePackage(packages: Package[]): Package | null {
  const availablePackages = packages.filter(pkg => isPackageAvailable(pkg))
  
  if (availablePackages.length === 0) {
    return null
  }
  
  return sortPackagesByAvailability(availablePackages)[0]
}
