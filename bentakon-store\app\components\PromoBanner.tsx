"use client"

import Image from "next/image"
import Link from "next/link"
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules'
import { useData } from "../contexts/DataContext"
import type { BannerSlide } from "../types"

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-fade'

export default function PromoBanner() {
  const { banners } = useData()

  // Get active banners sorted by order
  const activeBanners = banners.filter((banner) => banner.active).sort((a, b) => a.order - b.order)

  // Helper function to render banner content
  const renderBannerContent = (banner: BannerSlide) => {
    const content = (
      <div className="relative w-full h-full">
        <Image
          src={banner.image}
          alt={banner.title}
          fill
          className="object-cover"
          priority
          sizes="100vw"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = "/logo.jpg"
          }}
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/40" />
        
        {/* Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white px-4 max-w-2xl">
            <h2 className="text-2xl md:text-4xl font-bold mb-2 drop-shadow-lg">
              {banner.title}
            </h2>
            {banner.subtitle && (
              <p className="text-lg md:text-xl opacity-90 drop-shadow-lg">
                {banner.subtitle}
              </p>
            )}
          </div>
        </div>
      </div>
    )

    // Wrap with link if needed
    if (banner.linkType !== "none" && banner.linkValue) {
      if (banner.linkType === "custom") {
        return (
          <Link href={banner.linkValue} className="block w-full h-full">
            {content}
          </Link>
        )
      } else if (banner.linkType === "product") {
        return (
          <Link href={`/product/${banner.linkValue}`} className="block w-full h-full">
            {content}
          </Link>
        )
      }
    }

    return content
  }

  // Don't render if no banners
  if (activeBanners.length === 0) {
    return null
  }

  return (
    <div className="relative overflow-hidden rounded-xl shadow-2xl aspect-[2.33/1]">
      <Swiper
        modules={[Navigation, Pagination, Autoplay, EffectFade]}
        spaceBetween={0}
        slidesPerView={1}
        loop={activeBanners.length > 1}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet !bg-white/30 !w-2 !h-2',
          bulletActiveClass: 'swiper-pagination-bullet-active !bg-white !w-4 !h-2 !rounded-lg',
        }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }}
        effect="fade"
        fadeEffect={{
          crossFade: true
        }}
        className="w-full h-full"
        style={{
          '--swiper-pagination-bottom': '12px',
          '--swiper-pagination-bullet-inactive-opacity': '0.3',
          '--swiper-navigation-color': '#ffffff',
          '--swiper-navigation-size': '24px',
        } as any}
      >
        {activeBanners.map((banner, index) => (
          <SwiperSlide key={banner.id}>
            {renderBannerContent(banner)}
          </SwiperSlide>
        ))}

        {/* Custom Navigation Buttons */}
        {activeBanners.length > 1 && (
          <>
            <div className="swiper-button-prev !left-4 !w-10 !h-10 !bg-black/20 !backdrop-blur-sm !rounded-full !text-white hover:!bg-black/40 !transition-all !duration-300" />
            <div className="swiper-button-next !right-4 !w-10 !h-10 !bg-black/20 !backdrop-blur-sm !rounded-full !text-white hover:!bg-black/40 !transition-all !duration-300" />
          </>
        )}
      </Swiper>
    </div>
  )
}
