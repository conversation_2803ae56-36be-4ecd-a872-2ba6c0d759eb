// Product management utilities and types
// Extracted from ProductCRUD.tsx for better organization

export interface Product {
  id: string
  slug: string
  title: string
  description: string
  cover_image: string
  category_id?: string
  tags: string[]
  rating: number
  comment_count: number
  featured: boolean
  // Product-level pricing (used when no packages exist)
  original_price?: number
  user_price?: number
  discount_price?: number
  distributor_price?: number
  created_at: string
  updated_at: string
  packages: Package[]
  categories?: { id: string; name: string; slug: string }
  // Simplified custom fields
  customFields?: SimplifiedCustomField[]
}

export interface DigitalCode {
  id: string
  key: string
  used: boolean
  assignedToOrderId: string | null
  assignedAt?: string
}

export interface Package {
  id?: string
  name: string
  // Legacy price field for backward compatibility
  price?: number
  // Enhanced pricing fields
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
  image?: string
  description?: string
  use_product_image: boolean
  image_reference_type: 'url' | 'product_image'
  has_digital_codes: boolean
  digitalCodes?: DigitalCode[]
  hasDigitalCodes?: boolean
  availableCodesCount?: number
  // Manual inventory management fields
  manual_quantity?: number
  track_inventory?: boolean
  unlimited_stock?: boolean
}

// Simplified custom field interface - supports text and dropdown types
export interface SimplifiedCustomField {
  id?: string
  label: string
  description?: string
  required: boolean
  placeholder?: string
  field_type?: 'text' | 'dropdown'
  options?: Array<{ label: string; value: string }>
}

export interface Category {
  id: string
  name: string
  slug: string
}

// Initial form data for new products
export const getInitialFormData = (): Partial<Product> => ({
  title: '',
  description: '',
  category_id: '',
  tags: [],
  cover_image: '',
  packages: [],
  featured: false,
  // Product-level pricing
  original_price: undefined,
  user_price: undefined,
  discount_price: undefined,
  distributor_price: undefined
})

// Initial custom fields state
export const getInitialCustomFields = (): SimplifiedCustomField[] => []

// Create new package with smart defaults
export const createNewPackage = (): Package => ({
  id: Date.now().toString(),
  name: "",
  original_price: 0,
  user_price: 0,
  discount_price: undefined,
  distributor_price: undefined,
  image: "",
  use_product_image: true,
  image_reference_type: "product_image",
  has_digital_codes: false,
  hasDigitalCodes: false,
  digitalCodes: [],
  availableCodesCount: 0,
  // Default inventory settings - unlimited stock by default
  manual_quantity: 0,
  track_inventory: true,
  unlimited_stock: true
})

// Create new simplified custom field
export const createNewCustomField = (existingFields: SimplifiedCustomField[]): SimplifiedCustomField => ({
  id: Date.now().toString(),
  label: '',
  description: '',
  required: false,
  placeholder: '',
  field_type: 'text',
  options: []
})

// Pricing validation function
export const validatePricing = (original: number, user: number, discount?: number, distributor?: number): string[] => {
  const errors: string[] = []

  if (user <= original) {
    errors.push("سعر المستخدم يجب أن يكون أكبر من السعر الأصلي")
  }

  if (discount !== undefined) {
    if (discount >= user) {
      errors.push("سعر الخصم يجب أن يكون أقل من سعر المستخدم")
    }
    if (discount <= original) {
      errors.push("سعر الخصم يجب أن يكون أكبر من السعر الأصلي")
    }
    if (discount === user) {
      errors.push("سعر الخصم لا يمكن أن يساوي سعر المستخدم")
    }
  }

  if (distributor !== undefined) {
    if (distributor <= original) {
      errors.push("سعر الموزع يجب أن يكون أكبر من السعر الأصلي")
    }
    if (distributor >= user) {
      errors.push("سعر الموزع يجب أن يكون أقل من سعر المستخدم")
    }
  }

  return errors
}

// Calculate profit margin
export const calculateProfitMargin = (cost: number, price: number): number => {
  return price - cost
}

// Calculate discount percentage
export const calculateDiscount = (originalPrice: number, discountedPrice: number): number => {
  if (originalPrice <= 0 || discountedPrice >= originalPrice) return 0
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
}

// Parse digital codes from text input
export const parseDigitalCodes = (codesText: string): DigitalCode[] => {
  return codesText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .map((key, index) => ({
      id: `${Date.now()}-${index}`,
      key,
      used: false,
      assignedToOrderId: null,
    }))
}

// Get digital codes as text for display
export const getDigitalCodesText = (pkg: Package): string => {
  return pkg.digitalCodes?.map((code) => code.key).join("\n") || ""
}

// Check if product has packages (for smart pricing logic)
export const hasPackages = (formData: Partial<Product>): boolean => {
  return !!(formData.packages && formData.packages.length > 0)
}

// Validate complete form before submission
export const validateForm = (formData: Partial<Product>, customFields: SimplifiedCustomField[]): string[] => {
  const errors: string[] = []

  // Basic validation
  if (!formData.title?.trim()) {
    errors.push('عنوان المنتج مطلوب')
  }

  if (!formData.description?.trim()) {
    errors.push('وصف المنتج مطلوب')
  }

  if (!formData.category_id) {
    errors.push('فئة المنتج مطلوبة')
  }

  if (!formData.cover_image?.trim()) {
    errors.push('صورة الغلاف مطلوبة')
  }

  // Pricing validation based on whether packages exist
  if (!hasPackages(formData)) {
    // Product-level pricing validation
    if (!formData.original_price || !formData.user_price) {
      errors.push('يجب تحديد أسعار المنتج عند عدم وجود حزم')
    } else {
      const pricingErrors = validatePricing(
        formData.original_price,
        formData.user_price,
        formData.discount_price,
        formData.distributor_price
      )
      errors.push(...pricingErrors)
    }
  } else {
    // Package pricing validation
    formData.packages?.forEach((pkg, index) => {
      if (!pkg.name?.trim()) {
        errors.push(`اسم الحزمة ${index + 1} مطلوب`)
      }
      if (!pkg.original_price || !pkg.user_price) {
        errors.push(`أسعار الحزمة ${index + 1} مطلوبة`)
      } else {
        const pricingErrors = validatePricing(
          pkg.original_price,
          pkg.user_price,
          pkg.discount_price,
          pkg.distributor_price
        )
        if (pricingErrors.length > 0) {
          errors.push(`أخطاء في تسعير الحزمة "${pkg.name}": ${pricingErrors.join(', ')}`)
        }
      }
    })
  }

  return errors
}

// Prepare form data for API submission
export const prepareFormData = (formData: Partial<Product>, customFields: SimplifiedCustomField[]): any => {
  return {
    ...formData,
    customFields: customFields
  }
}
