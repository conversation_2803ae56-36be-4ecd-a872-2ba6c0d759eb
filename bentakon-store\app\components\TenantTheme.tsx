"use client"

import { useEffect } from "react"
import { useTenant } from "../contexts/TenantContext"

interface TenantThemeProps {
  children: React.ReactNode
}

export default function TenantTheme({ children }: TenantThemeProps) {
  const { tenant, getThemeValue } = useTenant()

  useEffect(() => {
    if (!tenant?.theme_config) return

    const root = document.documentElement
    const theme = tenant.theme_config

    // Apply CSS custom properties for theming
    root.style.setProperty('--tenant-primary', theme.primaryColor || '#3b82f6')
    root.style.setProperty('--tenant-secondary', theme.secondaryColor || '#1e40af')
    root.style.setProperty('--tenant-accent', theme.accentColor || '#f59e0b')
    root.style.setProperty('--tenant-background', theme.backgroundColor || '#111827')
    root.style.setProperty('--tenant-text', theme.textColor || '#ffffff')

    // Apply custom CSS if provided
    if (theme.customCSS) {
      let styleElement = document.getElementById('tenant-custom-css')
      if (!styleElement) {
        styleElement = document.createElement('style')
        styleElement.id = 'tenant-custom-css'
        document.head.appendChild(styleElement)
      }
      styleElement.textContent = theme.customCSS
    }

    // Update favicon if provided
    if (theme.favicon) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
      if (favicon) {
        favicon.href = theme.favicon
      } else {
        const newFavicon = document.createElement('link')
        newFavicon.rel = 'icon'
        newFavicon.href = theme.favicon
        document.head.appendChild(newFavicon)
      }
    }

    // Update page title with tenant name
    if (tenant.name) {
      const originalTitle = document.title
      if (!originalTitle.includes(tenant.name)) {
        document.title = `${tenant.name} - ${originalTitle}`
      }
    }

  }, [tenant])

  return <>{children}</>
}

// Hook for using tenant theme values in components
export function useTenantTheme() {
  const { tenant, getThemeValue } = useTenant()

  const getThemeColor = (colorKey: keyof NonNullable<typeof tenant>['theme_config']) => {
    return getThemeValue(colorKey) || getDefaultColor(colorKey)
  }

  const getDefaultColor = (colorKey: string) => {
    const defaults = {
      primaryColor: '#3b82f6',
      secondaryColor: '#1e40af',
      accentColor: '#f59e0b',
      backgroundColor: '#111827',
      textColor: '#ffffff'
    }
    return defaults[colorKey as keyof typeof defaults] || '#3b82f6'
  }

  const applyThemeToElement = (element: HTMLElement, property: string, colorKey: keyof NonNullable<typeof tenant>['theme_config']) => {
    const color = getThemeColor(colorKey)
    element.style.setProperty(property, color)
  }

  return {
    tenant,
    getThemeColor,
    applyThemeToElement,
    primaryColor: getThemeColor('primaryColor'),
    secondaryColor: getThemeColor('secondaryColor'),
    accentColor: getThemeColor('accentColor'),
    backgroundColor: getThemeColor('backgroundColor'),
    textColor: getThemeColor('textColor'),
    logo: getThemeValue('logo'),
    customCSS: getThemeValue('customCSS')
  }
}

// Component for displaying tenant logo
export function TenantLogo({ className = "", fallbackText }: { className?: string, fallbackText?: string }) {
  const { tenant } = useTenant()
  const logo = tenant?.theme_config?.logo
  const tenantName = tenant?.name || fallbackText || 'بنتاكون'

  if (logo) {
    return (
      <img
        src={logo}
        alt={tenantName}
        className={`h-8 w-auto ${className}`}
        onError={(e) => {
          // Fallback to text if image fails to load
          const target = e.target as HTMLImageElement
          target.style.display = 'none'
          const textElement = target.nextElementSibling as HTMLElement
          if (textElement) {
            textElement.style.display = 'block'
          }
        }}
      />
    )
  }

  return (
    <span className={`text-xl font-bold ${className}`}>
      {tenantName}
    </span>
  )
}

// Component for tenant-specific styling
export function TenantStyled({ 
  children, 
  className = "",
  useTheme = true,
  themeProperty = "backgroundColor"
}: { 
  children: React.ReactNode
  className?: string
  useTheme?: boolean
  themeProperty?: keyof NonNullable<typeof tenant>['theme_config']
}) {
  const { getThemeColor } = useTenantTheme()

  const style = useTheme ? {
    [themeProperty === 'backgroundColor' ? 'backgroundColor' : 
     themeProperty === 'primaryColor' ? 'color' : 
     themeProperty]: getThemeColor(themeProperty)
  } : {}

  return (
    <div className={className} style={style}>
      {children}
    </div>
  )
}
