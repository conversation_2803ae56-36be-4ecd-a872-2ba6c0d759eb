"use client"

import { Plus, Trash2, <PERSON><PERSON><PERSON>, AlertCircle, FileText, ChevronDown } from "lucide-react"
import type { Product, CustomField, Dropdown } from "../../types"
import FormField from "./FormField"
import Button from "./Button"

interface ProductCustomFieldsProps {
  formData: Partial<Product>
  onUpdate: (updates: Partial<Product>) => void
}

export default function ProductCustomFields({
  formData,
  onUpdate,
}: ProductCustomFieldsProps) {
  const customFields = formData.customFields || []
  const dropdowns = formData.dropdowns || []

  // Custom Fields Management
  const addCustomField = () => {
    const newField: CustomField = {
      id: Date.now().toString(),
      label: "",
      type: "text",
      required: false,
      placeholder: "",
    }
    onUpdate({
      customFields: [...customFields, newField],
    })
  }

  const updateCustomField = (index: number, field: keyof CustomField, value: any) => {
    const updatedFields = customFields.map((customField, i) =>
      i === index ? { ...customField, [field]: value } : customField
    )
    onUpdate({ customFields: updatedFields })
  }

  const removeCustomField = (index: number) => {
    const updatedFields = customFields.filter((_, i) => i !== index)
    onUpdate({ customFields: updatedFields })
  }

  // Dropdown Management
  const addDropdown = () => {
    const newDropdown: Dropdown = {
      id: Date.now().toString(),
      label: "",
      options: [],
      required: false,
      description: "",
    }
    onUpdate({
      dropdowns: [...dropdowns, newDropdown],
    })
  }

  const updateDropdown = (index: number, field: keyof Dropdown, value: any) => {
    const updatedDropdowns = dropdowns.map((dropdown, i) =>
      i === index ? { ...dropdown, [field]: value } : dropdown
    )
    onUpdate({ dropdowns: updatedDropdowns })
  }

  const removeDropdown = (index: number) => {
    const updatedDropdowns = dropdowns.filter((_, i) => i !== index)
    onUpdate({ dropdowns: updatedDropdowns })
  }

  const updateDropdownOptions = (index: number, optionsText: string) => {
    const options = optionsText
      .split('\n')
      .map(line => line.trim())
      .filter(Boolean)
    updateDropdown(index, 'options', options)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-gray-700/50 pb-4">
        <h3 className="text-xl font-bold text-white">الحقول المخصصة</h3>
        <p className="text-sm text-gray-400 mt-1">
          إضافة حقول مخصصة لجمع معلومات إضافية من العملاء عند الشراء
        </p>
      </div>

      {/* Text Input Fields Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <FileText className="w-5 h-5 text-purple-400" />
            <h4 className="text-lg font-semibold text-white">حقول النص</h4>
          </div>
          <Button
            onClick={addCustomField}
            variant="secondary"
            size="sm"
            className="flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="w-4 h-4" />
            <span>إضافة حقل نص</span>
          </Button>
        </div>

        {customFields.length === 0 ? (
          <div className="text-center py-8 bg-gray-700/20 rounded-lg border border-gray-600/30 border-dashed">
            <FileText className="w-8 h-8 text-gray-500 mx-auto mb-3" />
            <p className="text-sm text-gray-500 mb-3">لا توجد حقول نص مخصصة</p>
            <Button
              onClick={addCustomField}
              variant="primary"
              size="sm"
              className="flex items-center space-x-2 space-x-reverse mx-auto"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة حقل نص</span>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {customFields.map((field, index) => (
              <div
                key={field.id || index}
                className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50"
              >
                <div className="flex items-center justify-between mb-4">
                  <h5 className="font-semibold text-white">حقل نص {index + 1}</h5>
                  <Button
                    onClick={() => removeCustomField(index)}
                    variant="danger"
                    size="sm"
                    className="p-2"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <FormField label="تسمية الحقل" required>
                    <input
                      type="text"
                      value={field.label}
                      onChange={(e) => updateCustomField(index, "label", e.target.value)}
                      placeholder="مثل: معرف اللاعب"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>

                  <FormField label="نوع الحقل">
                    <select
                      value={field.type}
                      onChange={(e) => updateCustomField(index, "type", e.target.value)}
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    >
                      <option value="text">نص</option>
                      <option value="email">بريد إلكتروني</option>
                      <option value="number">رقم</option>
                    </select>
                  </FormField>

                  <FormField label="النص التوضيحي">
                    <input
                      type="text"
                      value={field.placeholder}
                      onChange={(e) => updateCustomField(index, "placeholder", e.target.value)}
                      placeholder="أدخل معرف اللاعب"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center space-x-2 space-x-reverse cursor-pointer">
                    <input
                      type="checkbox"
                      checked={field.required}
                      onChange={(e) => updateCustomField(index, "required", e.target.checked)}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-white">حقل مطلوب</span>
                  </label>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Dropdown Fields Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <ChevronDown className="w-5 h-5 text-blue-400" />
            <h4 className="text-lg font-semibold text-white">القوائم المنسدلة</h4>
          </div>
          <Button
            onClick={addDropdown}
            variant="secondary"
            size="sm"
            className="flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="w-4 h-4" />
            <span>إضافة قائمة منسدلة</span>
          </Button>
        </div>

        {dropdowns.length === 0 ? (
          <div className="text-center py-8 bg-gray-700/20 rounded-lg border border-gray-600/30 border-dashed">
            <ChevronDown className="w-8 h-8 text-gray-500 mx-auto mb-3" />
            <p className="text-sm text-gray-500 mb-3">لا توجد قوائم منسدلة</p>
            <Button
              onClick={addDropdown}
              variant="primary"
              size="sm"
              className="flex items-center space-x-2 space-x-reverse mx-auto"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة قائمة منسدلة</span>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {dropdowns.map((dropdown, index) => (
              <div
                key={dropdown.id || index}
                className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50"
              >
                <div className="flex items-center justify-between mb-4">
                  <h5 className="font-semibold text-white">قائمة منسدلة {index + 1}</h5>
                  <Button
                    onClick={() => removeDropdown(index)}
                    variant="danger"
                    size="sm"
                    className="p-2"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                  <FormField label="تسمية القائمة" required>
                    <input
                      type="text"
                      value={dropdown.label}
                      onChange={(e) => updateDropdown(index, "label", e.target.value)}
                      placeholder="مثل: الخادم"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>

                  <FormField label="الوصف" description="وصف اختياري للقائمة">
                    <input
                      type="text"
                      value={dropdown.description || ""}
                      onChange={(e) => updateDropdown(index, "description", e.target.value)}
                      placeholder="اختر الخادم المناسب"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>
                </div>

                <FormField label="خيارات القائمة" required description="أدخل خيار واحد في كل سطر">
                  <textarea
                    value={dropdown.options.join('\n')}
                    onChange={(e) => updateDropdownOptions(index, e.target.value)}
                    placeholder="أدخل خيارات القائمة (خيار واحد في كل سطر)&#10;مثال:&#10;الخادم الأول&#10;الخادم الثاني&#10;الخادم الثالث"
                    rows={4}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
                  />
                </FormField>

                <div className="flex items-center justify-between mt-4">
                  <label className="flex items-center space-x-2 space-x-reverse cursor-pointer">
                    <input
                      type="checkbox"
                      checked={dropdown.required}
                      onChange={(e) => updateDropdown(index, "required", e.target.checked)}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-white">قائمة مطلوبة</span>
                  </label>
                  {dropdown.options.length > 0 && (
                    <div className="text-sm text-green-400">
                      {dropdown.options.length} خيار متاح
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Guidelines */}
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-semibold text-yellow-300 mb-2">إرشادات الحقول المخصصة</h4>
            <div className="text-xs text-yellow-200 space-y-1">
              <p>• استخدم حقول النص لجمع معلومات مثل معرف اللاعب أو اسم المستخدم</p>
              <p>• استخدم القوائم المنسدلة للخيارات المحددة مثل الخوادم أو المناطق</p>
              <p>• الحقول المطلوبة يجب ملؤها قبل إتمام الشراء</p>
              <p>• استخدم أسماء واضحة ومفهومة للحقول</p>
              <p>• أضف نصوص توضيحية لمساعدة العملاء</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
