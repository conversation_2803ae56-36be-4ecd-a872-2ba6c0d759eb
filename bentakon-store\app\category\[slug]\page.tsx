"use client"

import { useState, useEffect, useMemo } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { ArrowRight, Tag, Search } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import ProductCard from "../../components/ProductCard"
import Pagination from "../../components/Pagination"
import { LoadingSpinner } from "../../components/LoadingStates"
import { useTenant } from "../../contexts/TenantContext"
import { useData } from "../../contexts/DataContext"
import { getCategories } from "../../lib/categories"
import type { Category } from "../../types"

export default function CategoryDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { tenant } = useTenant()
  const { products, currentUser } = useData()
  const [category, setCategory] = useState<Category | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const productsPerPage = 20

  const userRole = currentUser?.role || "user"
  const categorySlug = params.slug as string

  // Load category when component mounts
  useEffect(() => {
    if (tenant && categorySlug) {
      loadCategory()
    }
  }, [tenant, categorySlug])

  const loadCategory = async () => {
    if (!tenant) return

    setIsLoading(true)
    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        const foundCategory = result.data.find(cat => cat.slug === categorySlug)
        if (foundCategory) {
          setCategory(foundCategory)
        } else {
          // Category not found, redirect to categories page
          router.push('/categories')
        }
      }
    } catch (error) {
      console.error('Error loading category:', error)
      router.push('/categories')
    } finally {
      setIsLoading(false)
    }
  }

  // Filter products by category
  const categoryProducts = useMemo(() => {
    if (!category) return []
    
    return products.filter(product => 
      product.category_id === category.id || product.category === category.name
    )
  }, [products, category])

  // Filter products based on search query
  const filteredProducts = useMemo(() => {
    if (!searchQuery.trim()) return categoryProducts
    
    const query = searchQuery.toLowerCase()
    return categoryProducts.filter(product =>
      product.title.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query) ||
      product.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }, [categoryProducts, searchQuery])

  // Sort products by popularity
  const sortedProducts = useMemo(() => {
    return [...filteredProducts].sort((a, b) => b.commentCount - a.commentCount)
  }, [filteredProducts])

  // Pagination logic
  const { paginatedProducts, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex)
    const totalPages = Math.ceil(sortedProducts.length / productsPerPage)

    return { paginatedProducts, totalPages }
  }, [sortedProducts, currentPage, productsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <LoadingSpinner size="lg" className="text-purple-500 mx-auto mb-4" />
              <p className="text-gray-400">جاري تحميل الفئة...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!category) {
    return null // Will redirect to categories page
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400 mb-6">
          <Link href="/" className="hover:text-purple-400 transition-colors">
            الرئيسية
          </Link>
          <ArrowRight className="w-4 h-4" />
          <Link href="/categories" className="hover:text-purple-400 transition-colors">
            الفئات
          </Link>
          <ArrowRight className="w-4 h-4" />
          <span className="text-white">{category.name || category.slug}</span>
        </nav>

        {/* Category Header */}
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center gap-6">
            {/* Category Image */}
            <div className="w-24 h-24 md:w-32 md:h-32 rounded-xl overflow-hidden bg-gray-700/50 flex-shrink-0">
              <Image
                src={category.image}
                alt={category.name || category.slug}
                width={128}
                height={128}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = "/logo.jpg"
                }}
              />
            </div>

            {/* Category Info */}
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {category.name || category.slug}
              </h1>
              {category.description && (
                <p className="text-gray-400 mb-4">
                  {category.description}
                </p>
              )}
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                <span>{categoryProducts.length} منتج</span>
                <span>•</span>
                <span>الرابط: {category.slug}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h2 className="text-xl font-semibold text-white">
              المنتجات في هذه الفئة
            </h2>
            <p className="text-gray-400">
              {searchQuery 
                ? `${filteredProducts.length} منتج مطابق للبحث`
                : `${categoryProducts.length} منتج متاح`
              }
            </p>
          </div>

          {/* Search */}
          <div className="relative w-full sm:w-80">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث في المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            />
          </div>
        </div>

        {/* Products Grid */}
        {paginatedProducts.length === 0 ? (
          <div className="text-center py-16">
            <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">
              {searchQuery ? 'لا توجد منتجات مطابقة' : 'لا توجد منتجات في هذه الفئة'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchQuery 
                ? 'جرب البحث بكلمات مختلفة' 
                : 'لم يتم إضافة منتجات لهذه الفئة بعد'
              }
            </p>
            {searchQuery ? (
              <button
                onClick={() => setSearchQuery("")}
                className="btn-secondary"
              >
                مسح البحث
              </button>
            ) : (
              <Link href="/categories" className="btn-secondary">
                تصفح الفئات الأخرى
              </Link>
            )}
          </div>
        ) : (
          <>
            {/* Products Grid - Same responsive pattern as shop page */}
            <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4 mb-8">
              {paginatedProducts.map((product) => (
                <ProductCard key={product.id} product={product} userRole={userRole} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                className="mt-8"
              />
            )}
          </>
        )}
      </div>
    </div>
  )
}
