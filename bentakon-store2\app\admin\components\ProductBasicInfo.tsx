"use client"

import { Upload } from "lucide-react"
import type { Product } from "../../types"
import FormField from "./FormField"
import Button from "./Button"

interface ProductBasicInfoProps {
  formData: Partial<Product>
  onUpdate: (updates: Partial<Product>) => void
  onSave: () => void
  onCancel: () => void
  isSubmitting: boolean
  isEditing: boolean
}

export default function ProductBasicInfo({
  formData,
  onUpdate,
  onSave,
  onCancel,
  isSubmitting,
  isEditing,
}: ProductBasicInfoProps) {
  const handleFieldChange = (field: keyof Product, value: any) => {
    onUpdate({ [field]: value })
  }

  const handleTagsChange = (value: string) => {
    const tags = value
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean)
    onUpdate({ tags })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-700/50 pb-4">
        <h3 className="text-xl font-bold text-white">المعلومات الأساسية</h3>
        <p className="text-sm text-gray-400 mt-1">
          أدخل المعلومات الأساسية للمنتج مثل الاسم والوصف والفئة
        </p>
      </div>

      {/* Basic Information Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Product Title */}
        <FormField
          label="عنوان المنتج"
          required
          error={!formData.title ? "عنوان المنتج مطلوب" : undefined}
        >
          <input
            type="text"
            value={formData.title || ""}
            onChange={(e) => handleFieldChange("title", e.target.value)}
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            placeholder="أدخل عنوان المنتج"
            disabled={isSubmitting}
          />
        </FormField>

        {/* Category */}
        <FormField
          label="الفئة"
          required
          error={!formData.category ? "الفئة مطلوبة" : undefined}
        >
          <input
            type="text"
            value={formData.category || ""}
            onChange={(e) => handleFieldChange("category", e.target.value)}
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            placeholder="مثل: MOBA, RPG, باتل رويال"
            disabled={isSubmitting}
          />
        </FormField>
      </div>

      {/* Product Description */}
      <FormField
        label="وصف المنتج"
        required
        error={!formData.description ? "وصف المنتج مطلوب" : undefined}
      >
        <textarea
          value={formData.description || ""}
          onChange={(e) => handleFieldChange("description", e.target.value)}
          rows={4}
          className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
          placeholder="وصف مفصل لعرض هذا المنتج..."
          disabled={isSubmitting}
        />
      </FormField>

      {/* Cover Image */}
      <FormField
        label="صورة الغلاف"
        required
        error={!formData.coverImage ? "صورة الغلاف مطلوبة" : undefined}
      >
        <div className="flex items-center space-x-3 space-x-reverse">
          <input
            type="url"
            value={formData.coverImage || ""}
            onChange={(e) => handleFieldChange("coverImage", e.target.value)}
            className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            placeholder="رابط صورة الغلاف"
            disabled={isSubmitting}
          />
          <Button
            variant="secondary"
            size="sm"
            disabled={isSubmitting}
            className="flex items-center space-x-2 space-x-reverse"
          >
            <Upload className="w-4 h-4" />
            <span>رفع</span>
          </Button>
        </div>
        {formData.coverImage && (
          <div className="mt-3">
            <img
              src={formData.coverImage}
              alt="معاينة صورة الغلاف"
              className="w-32 h-20 object-cover rounded-lg border border-gray-600/50"
              onError={(e) => {
                e.currentTarget.style.display = "none"
              }}
            />
          </div>
        )}
      </FormField>

      {/* Keywords/Tags */}
      <FormField
        label="الكلمات المفتاحية"
        description="أدخل الكلمات المفتاحية مفصولة بفاصلة لتحسين البحث"
      >
        <input
          type="text"
          value={formData.tags?.join(", ") || ""}
          onChange={(e) => handleTagsChange(e.target.value)}
          className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
          placeholder="شائع, مميز, جديد, آر بي جي"
          disabled={isSubmitting}
        />
        {formData.tags && formData.tags.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {formData.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full border border-purple-600/30"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </FormField>

      {/* Product Status Toggles */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-white">حالة المنتج</h4>
        <div className="flex flex-col sm:flex-row sm:space-x-6 sm:space-x-reverse space-y-3 sm:space-y-0">
          <label className="flex items-center space-x-3 space-x-reverse cursor-pointer">
            <input
              type="checkbox"
              checked={formData.featured || false}
              onChange={(e) => handleFieldChange("featured", e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
              disabled={isSubmitting}
            />
            <div className="flex flex-col">
              <span className="text-sm font-medium text-white">منتج مميز</span>
              <span className="text-xs text-gray-400">سيظهر في القسم المميز</span>
            </div>
          </label>

          <label className="flex items-center space-x-3 space-x-reverse cursor-pointer">
            <input
              type="checkbox"
              checked={formData.popular || false}
              onChange={(e) => handleFieldChange("popular", e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
              disabled={isSubmitting}
            />
            <div className="flex flex-col">
              <span className="text-sm font-medium text-white">منتج شائع</span>
              <span className="text-xs text-gray-400">سيظهر في القسم الشائع</span>
            </div>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
        <Button
          onClick={onSave}
          loading={isSubmitting}
          className="flex-1"
          disabled={!formData.title || !formData.category || !formData.description || !formData.coverImage}
        >
          {isEditing ? "تحديث المنتج" : "إضافة المنتج"}
        </Button>
        <Button
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
          className="flex-1"
        >
          إلغاء
        </Button>
      </div>
    </div>
  )
}
