"use client"

import { useState } from "react"
import { Plus, Trash2, Key, AlertCircle, Package, DollarSign } from "lucide-react"
import type { Product, Package, DigitalCode } from "../../types"
import FormField from "./FormField"
import Button from "./Button"

interface ProductPackagesProps {
  formData: Partial<Product>
  onUpdate: (updates: Partial<Product>) => void
}

export default function ProductPackages({
  formData,
  onUpdate,
}: ProductPackagesProps) {
  const packages = formData.packages || []

  const addPackage = () => {
    const newPackage: Package = {
      id: Date.now().toString(),
      name: "",
      price: 0,
      image: "",
      hasDigitalCodes: false,
      digitalCodes: [],
    }
    onUpdate({
      packages: [...packages, newPackage],
    })
  }

  const updatePackage = (index: number, field: keyof Package, value: any) => {
    const updatedPackages = packages.map((pkg, i) =>
      i === index ? { ...pkg, [field]: value } : pkg
    )
    onUpdate({ packages: updatedPackages })
  }

  const removePackage = (index: number) => {
    const updatedPackages = packages.filter((_, i) => i !== index)
    onUpdate({ packages: updatedPackages })
  }

  const getPackageDigitalCodesText = (pkg: Package): string => {
    return pkg.digitalCodes?.map(code => code.key).join('\n') || ''
  }

  const updatePackageDigitalCodes = (index: number, codesText: string) => {
    const codes = codesText
      .split('\n')
      .map(line => line.trim())
      .filter(Boolean)
      .map((key, codeIndex) => ({
        id: `${Date.now()}-${codeIndex}`,
        key,
        used: false,
        assignedToOrderId: null,
      }))

    updatePackage(index, 'digitalCodes', codes)
    updatePackage(index, 'hasDigitalCodes', codes.length > 0)
    updatePackage(index, 'availableCodesCount', codes.length)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-700/50 pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-white">الحزم</h3>
            <p className="text-sm text-gray-400 mt-1">
              إدارة حزم المنتج وأسعارها والأكواد الرقمية
            </p>
          </div>
          <Button
            onClick={addPackage}
            variant="secondary"
            size="sm"
            className="flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="w-4 h-4" />
            <span>إضافة حزمة</span>
          </Button>
        </div>
      </div>

      {/* Packages List */}
      {packages.length === 0 ? (
        <div className="text-center py-12 bg-gray-700/20 rounded-lg border border-gray-600/30 border-dashed">
          <Package className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-gray-400 mb-2">لا توجد حزم</h4>
          <p className="text-sm text-gray-500 mb-4">
            أضف حزمة واحدة على الأقل لهذا المنتج
          </p>
          <Button
            onClick={addPackage}
            variant="primary"
            size="sm"
            className="flex items-center space-x-2 space-x-reverse mx-auto"
          >
            <Plus className="w-4 h-4" />
            <span>إضافة أول حزمة</span>
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {packages.map((pkg, index) => (
            <div
              key={pkg.id || index}
              className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-6 border border-gray-600/50"
            >
              {/* Package Header */}
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-white flex items-center space-x-2 space-x-reverse">
                  <Package className="w-5 h-5 text-purple-400" />
                  <span>حزمة {index + 1}</span>
                </h4>
                <Button
                  onClick={() => removePackage(index)}
                  variant="danger"
                  size="sm"
                  className="p-2"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              {/* Basic Package Info */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <FormField label="اسم الحزمة" required>
                  <input
                    type="text"
                    value={pkg.name}
                    onChange={(e) => updatePackage(index, "name", e.target.value)}
                    placeholder="مثل: 60 جوهرة نجمية"
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  />
                </FormField>

                <FormField label="صورة الحزمة">
                  <input
                    type="url"
                    value={pkg.image}
                    onChange={(e) => updatePackage(index, "image", e.target.value)}
                    placeholder="رابط صورة الحزمة أو اتركه فارغاً لاستخدام صورة المنتج"
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  />
                </FormField>
              </div>

              {/* Package Description */}
              <FormField label="وصف الحزمة" description="وصف اختياري للحزمة">
                <textarea
                  value={pkg.description || ""}
                  onChange={(e) => updatePackage(index, "description", e.target.value)}
                  placeholder="وصف الحزمة (اختياري)"
                  rows={2}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
                />
              </FormField>

              {/* Pricing Section */}
              <div className="border-t border-gray-600/50 pt-4 mt-4">
                <h5 className="font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                  <DollarSign className="w-4 h-4 text-green-400" />
                  <span>التسعير</span>
                </h5>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <FormField label="السعر الحالي" required>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={pkg.price}
                      onChange={(e) => updatePackage(index, "price", Number(e.target.value))}
                      placeholder="0.00"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>

                  <FormField label="السعر الأصلي" description="قبل الخصم">
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={pkg.originalPrice || ""}
                      onChange={(e) => updatePackage(index, "originalPrice", Number(e.target.value) || undefined)}
                      placeholder="0.00"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>

                  <FormField label="نسبة الخصم" description="بالنسبة المئوية">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={pkg.discount || ""}
                      onChange={(e) => updatePackage(index, "discount", Number(e.target.value) || undefined)}
                      placeholder="0"
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    />
                  </FormField>

                  {/* Price Summary */}
                  <div className="flex flex-col justify-center">
                    <div className="text-xs text-gray-400 mb-1">ملخص السعر</div>
                    <div className="text-lg font-bold text-green-400">${pkg.price.toFixed(2)}</div>
                    {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                      <div className="text-xs text-gray-500 line-through">${pkg.originalPrice.toFixed(2)}</div>
                    )}
                    {pkg.discount && (
                      <div className="text-xs text-orange-400">{pkg.discount}% خصم</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Digital Codes Section */}
              <div className="border-t border-gray-600/50 pt-4 mt-4">
                <div className="flex items-center space-x-2 space-x-reverse mb-3">
                  <Key className="w-5 h-5 text-blue-400" />
                  <h5 className="font-semibold text-blue-400">الأكواد الرقمية</h5>
                  <span className="text-sm text-gray-400">(اختياري)</span>
                </div>

                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                  <p className="text-sm text-blue-300 mb-2">💡 إرشادات الأكواد الرقمية:</p>
                  <ul className="text-xs text-blue-200 space-y-1">
                    <li>• أدخل كود واحد في كل سطر</li>
                    <li>• سيتم تخصيص كود واحد فقط لكل طلب</li>
                    <li>• الأكواد المستخدمة لن تظهر للمشترين الآخرين</li>
                    <li>• إذا نفدت الأكواد، ستصبح الحزمة غير متاحة</li>
                  </ul>
                </div>

                <FormField label="الأكواد الرقمية" description="أدخل كود واحد في كل سطر">
                  <textarea
                    value={getPackageDigitalCodesText(pkg)}
                    onChange={(e) => updatePackageDigitalCodes(index, e.target.value)}
                    placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12&#10;9GHT-LMK3-992Z"
                    rows={4}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 font-mono text-sm resize-none"
                  />
                </FormField>

                {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                  <div className="mt-2 text-sm text-green-400 flex items-center space-x-2 space-x-reverse">
                    <Key className="w-4 h-4" />
                    <span>تم إضافة {pkg.digitalCodes.length} كود رقمي</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Package Guidelines */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-semibold text-blue-300 mb-2">إرشادات الحزم</h4>
            <div className="text-xs text-blue-200 space-y-1">
              <p>• أضف حزمة واحدة على الأقل لكل منتج</p>
              <p>• استخدم أسماء واضحة ومفهومة للحزم</p>
              <p>• يمكن ترك صورة الحزمة فارغة لاستخدام صورة المنتج الأساسية</p>
              <p>• السعر الحالي هو السعر المعروض للعملاء</p>
              <p>• السعر الأصلي ونسبة الخصم اختياريان لعرض العروض</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
