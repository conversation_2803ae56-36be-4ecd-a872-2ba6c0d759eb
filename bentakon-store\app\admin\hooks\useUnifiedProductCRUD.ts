import { useState, useCallback } from 'react'

interface UnifiedProductCRUDOptions {
  onSuccess?: (message: string) => void
  onError?: (error: string) => void
}

interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  category?: string
  featured?: boolean | null
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function useUnifiedProductCRUD(options: UnifiedProductCRUDOptions = {}) {
  const [loading, setLoading] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)

  const callUnifiedAPI = useCallback(async (operation: string, data?: any, id?: string) => {
    const response = await fetch('/api/admin/products/unified', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        operation,
        data,
        id
      })
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error || 'حدث خطأ غير متوقع')
    }

    return result
  }, [])

  // Create product
  const createProduct = useCallback(async (productData: any) => {
    try {
      setActionLoading(true)
      const result = await callUnifiedAPI('create', productData)
      options.onSuccess?.(result.message || 'تم إنشاء المنتج بنجاح')
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'فشل في إنشاء المنتج'
      options.onError?.(errorMessage)
      throw error
    } finally {
      setActionLoading(false)
    }
  }, [callUnifiedAPI, options])

  // Update product
  const updateProduct = useCallback(async (id: string, productData: any) => {
    try {
      setActionLoading(true)
      const result = await callUnifiedAPI('update', productData, id)
      options.onSuccess?.(result.message || 'تم تحديث المنتج بنجاح')
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'فشل في تحديث المنتج'
      options.onError?.(errorMessage)
      throw error
    } finally {
      setActionLoading(false)
    }
  }, [callUnifiedAPI, options])

  // Delete product
  const deleteProduct = useCallback(async (id: string) => {
    try {
      setActionLoading(true)
      const result = await callUnifiedAPI('delete', undefined, id)
      options.onSuccess?.(result.message || 'تم حذف المنتج بنجاح')
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'فشل في حذف المنتج'
      options.onError?.(errorMessage)
      throw error
    } finally {
      setActionLoading(false)
    }
  }, [callUnifiedAPI, options])

  // Get single product
  const getProduct = useCallback(async (id: string) => {
    try {
      setLoading(true)
      const result = await callUnifiedAPI('get', undefined, id)
      return result.product
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'فشل في جلب المنتج'
      options.onError?.(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }, [callUnifiedAPI, options])

  // List products with pagination and filtering
  const listProducts = useCallback(async (params: PaginationParams = {}) => {
    try {
      setLoading(true)
      const result = await callUnifiedAPI('list', params)
      return {
        products: result.products,
        pagination: result.pagination
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'فشل في جلب المنتجات'
      options.onError?.(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }, [callUnifiedAPI, options])

  // Save product (create or update based on whether ID exists)
  const saveProduct = useCallback(async (productData: any, id?: string) => {
    if (id) {
      return await updateProduct(id, productData)
    } else {
      return await createProduct(productData)
    }
  }, [createProduct, updateProduct])

  return {
    // State
    loading,
    actionLoading,
    
    // Methods
    createProduct,
    updateProduct,
    deleteProduct,
    getProduct,
    listProducts,
    saveProduct,
    
    // Direct API call for custom operations
    callUnifiedAPI
  }
}

// Hook for managing product list state
export function useProductList() {
  const [products, setProducts] = useState<any[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [filters, setFilters] = useState<PaginationParams>({
    page: 1,
    limit: 10,
    search: '',
    category: '',
    featured: null,
    sortBy: 'created_at',
    sortOrder: 'desc' as const
  })

  const { loading, actionLoading, listProducts, deleteProduct, saveProduct } = useUnifiedProductCRUD({
    onSuccess: (message) => {
      console.log('Success:', message)
    },
    onError: (error) => {
      console.error('Error:', error)
    }
  })

  // Fetch products
  const fetchProducts = useCallback(async () => {
    try {
      const result = await listProducts(filters)
      setProducts(result.products)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Failed to fetch products:', error)
    }
  }, [listProducts, filters])

  // Update filters and refetch
  const updateFilters = useCallback((newFilters: Partial<PaginationParams>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }))
  }, [])

  // Pagination methods
  const goToPage = useCallback((page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }, [])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      setFilters(prev => ({ ...prev, page: prev.page! + 1 }))
    }
  }, [pagination.hasNext])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      setFilters(prev => ({ ...prev, page: prev.page! - 1 }))
    }
  }, [pagination.hasPrev])

  // CRUD operations that update local state
  const createItem = useCallback(async (data: any) => {
    const result = await saveProduct(data)
    await fetchProducts() // Refresh list
    return result
  }, [saveProduct, fetchProducts])

  const updateItem = useCallback(async (id: string, data: any) => {
    const result = await saveProduct(data, id)
    await fetchProducts() // Refresh list
    return result
  }, [saveProduct, fetchProducts])

  const deleteItem = useCallback(async (id: string) => {
    const result = await deleteProduct(id)
    await fetchProducts() // Refresh list
    return result
  }, [deleteProduct, fetchProducts])

  return {
    // State
    products,
    loading,
    actionLoading,
    pagination,
    filters,
    
    // Methods
    fetchProducts,
    updateFilters,
    goToPage,
    nextPage,
    prevPage,
    createItem,
    updateItem,
    deleteItem
  }
}
