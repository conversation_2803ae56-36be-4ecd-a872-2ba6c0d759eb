import React, { useState, useCallback } from 'react'
import { Search, Filter, X } from 'lucide-react'

export interface FilterOption {
  key: string
  label: string
  type: 'text' | 'select' | 'boolean'
  options?: { value: string; label: string }[]
  placeholder?: string
}

interface AdminFiltersProps {
  filters: Record<string, any>
  onFiltersChange: (filters: Record<string, any>) => void
  filterOptions: FilterOption[]
  searchPlaceholder?: string
}

export default function AdminFilters({
  filters,
  onFiltersChange,
  filterOptions,
  searchPlaceholder = "البحث..."
}: AdminFiltersProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [localFilters, setLocalFilters] = useState(filters)

  const handleSearchChange = useCallback((value: string) => {
    const newFilters = { ...localFilters, search: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }, [localFilters, onFiltersChange])

  const handleFilterChange = useCallback((key: string, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }, [localFilters, onFiltersChange])

  const clearFilters = useCallback(() => {
    const clearedFilters = { search: '' }
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }, [onFiltersChange])

  const hasActiveFilters = Object.values(localFilters).some(value => 
    value !== '' && value !== null && value !== undefined
  )

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-4">
      <div className="space-y-4">
        {/* Search and Filter Toggle */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={localFilters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder={searchPlaceholder}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pl-3 pr-10 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
            />
          </div>

          {/* Filter Controls */}
          <div className="flex items-center gap-2">
            {filterOptions.length > 0 && (
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                  showAdvancedFilters
                    ? 'bg-purple-600/20 border-purple-500/50 text-purple-400'
                    : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500/50'
                }`}
              >
                <Filter className="w-4 h-4" />
                <span className="text-sm font-medium">فلاتر متقدمة</span>
              </button>
            )}

            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="flex items-center gap-2 px-4 py-2 bg-red-600/20 border border-red-500/50 text-red-400 rounded-lg hover:bg-red-600/30 transition-all duration-300"
              >
                <X className="w-4 h-4" />
                <span className="text-sm font-medium">مسح</span>
              </button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-700/50">
            {filterOptions.map((option) => (
              <div key={option.key}>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {option.label}
                </label>
                
                {option.type === 'text' && (
                  <input
                    type="text"
                    value={localFilters[option.key] || ''}
                    onChange={(e) => handleFilterChange(option.key, e.target.value)}
                    placeholder={option.placeholder}
                    className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                  />
                )}

                {option.type === 'select' && option.options && (
                  <select
                    value={localFilters[option.key] || ''}
                    onChange={(e) => handleFilterChange(option.key, e.target.value)}
                    className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                  >
                    <option value="">الكل</option>
                    {option.options.map((opt) => (
                      <option key={opt.value} value={opt.value}>
                        {opt.label}
                      </option>
                    ))}
                  </select>
                )}

                {option.type === 'boolean' && (
                  <div className="flex items-center gap-3">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name={option.key}
                        checked={localFilters[option.key] === true}
                        onChange={() => handleFilterChange(option.key, true)}
                        className="text-purple-600 focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">نعم</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name={option.key}
                        checked={localFilters[option.key] === false}
                        onChange={() => handleFilterChange(option.key, false)}
                        className="text-purple-600 focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">لا</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name={option.key}
                        checked={localFilters[option.key] === undefined || localFilters[option.key] === ''}
                        onChange={() => handleFilterChange(option.key, '')}
                        className="text-purple-600 focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">الكل</span>
                    </label>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
