import React from "react"
import { Loader2 } from "lucide-react"

// Generic loading spinner
export function LoadingSpinner({ size = "default", className = "" }: { 
  size?: "sm" | "default" | "lg"
  className?: string 
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    default: "w-6 h-6", 
    lg: "w-8 h-8"
  }

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  )
}

// Full page loading
export function PageLoading() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="text-purple-500 mx-auto mb-4" />
        <p className="text-gray-400">جاري التحميل...</p>
      </div>
    </div>
  )
}

// Button loading state
export function LoadingButton({ 
  children, 
  loading, 
  disabled,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  loading: boolean
  disabled?: boolean
  className?: string
  [key: string]: any
}) {
  return (
    <button
      disabled={loading || disabled}
      className={`relative ${className} ${loading ? 'cursor-not-allowed opacity-75' : ''}`}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" className="text-current" />
        </div>
      )}
      <span className={loading ? 'invisible' : 'visible'}>
        {children}
      </span>
    </button>
  )
}

// Card skeleton loader
export function CardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700/30 animate-pulse">
      <div className="aspect-square bg-gray-700"></div>
      <div className="p-4 space-y-3">
        <div className="h-4 bg-gray-700 rounded w-3/4"></div>
        <div className="h-3 bg-gray-700 rounded w-1/2"></div>
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-700 rounded w-1/4"></div>
          <div className="h-6 bg-gray-700 rounded w-16"></div>
        </div>
      </div>
    </div>
  )
}

// Product grid skeleton
export function ProductGridSkeleton({ count = 8 }: { count?: number }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <CardSkeleton key={index} />
      ))}
    </div>
  )
}

// Table skeleton
export function TableSkeleton({ rows = 5, columns = 4 }: { rows?: number, columns?: number }) {
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-700 rounded animate-pulse"></div>
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="h-3 bg-gray-800 rounded animate-pulse"></div>
          ))}
        </div>
      ))}
    </div>
  )
}

// Form skeleton
export function FormSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-1/4"></div>
        <div className="h-10 bg-gray-800 rounded"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-1/3"></div>
        <div className="h-10 bg-gray-800 rounded"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-1/5"></div>
        <div className="h-24 bg-gray-800 rounded"></div>
      </div>
      <div className="flex gap-3">
        <div className="h-10 bg-gray-700 rounded w-24"></div>
        <div className="h-10 bg-gray-800 rounded w-20"></div>
      </div>
    </div>
  )
}

// Stats skeleton
export function StatsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="bg-gray-800 rounded-lg p-6 animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="w-8 h-8 bg-gray-700 rounded"></div>
            <div className="w-4 h-4 bg-gray-700 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="h-8 bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Banner skeleton
export function BannerSkeleton() {
  return (
    <div className="relative w-full h-64 md:h-80 lg:h-96 bg-gray-800 rounded-xl overflow-hidden animate-pulse">
      <div className="absolute inset-0 bg-gray-700"></div>
      <div className="absolute bottom-6 left-6 right-6">
        <div className="h-8 bg-gray-600 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-600 rounded w-1/2"></div>
      </div>
    </div>
  )
}

// Search skeleton
export function SearchSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-10 bg-gray-800 rounded animate-pulse"></div>
      <div className="flex gap-2">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="h-8 bg-gray-800 rounded w-20 animate-pulse"></div>
        ))}
      </div>
    </div>
  )
}

// Navigation skeleton
export function NavigationSkeleton() {
  return (
    <div className="flex items-center space-x-6 space-x-reverse animate-pulse">
      <div className="w-8 h-8 bg-gray-700 rounded"></div>
      <div className="h-4 bg-gray-700 rounded w-20"></div>
      <div className="flex space-x-4 space-x-reverse">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-700 rounded w-16"></div>
        ))}
      </div>
      <div className="h-8 bg-gray-700 rounded w-24"></div>
    </div>
  )
}

// Generic content skeleton
export function ContentSkeleton({ 
  lines = 3, 
  className = "" 
}: { 
  lines?: number
  className?: string 
}) {
  return (
    <div className={`space-y-3 animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index} 
          className={`h-4 bg-gray-700 rounded ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        ></div>
      ))}
    </div>
  )
}

// Loading overlay
export function LoadingOverlay({ 
  show, 
  message = "جاري التحميل..." 
}: { 
  show: boolean
  message?: string 
}) {
  if (!show) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 text-center border border-gray-700">
        <LoadingSpinner size="lg" className="text-purple-500 mx-auto mb-4" />
        <p className="text-white">{message}</p>
      </div>
    </div>
  )
}

// Inline loading
export function InlineLoading({ 
  message = "جاري التحميل...",
  className = "" 
}: { 
  message?: string
  className?: string 
}) {
  return (
    <div className={`flex items-center justify-center py-8 ${className}`}>
      <LoadingSpinner className="text-purple-500 mr-3" />
      <span className="text-gray-400">{message}</span>
    </div>
  )
}
