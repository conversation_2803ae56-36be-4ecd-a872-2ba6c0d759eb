import { config } from './config'

// Error tracking and monitoring utilities
export class MonitoringService {
  private static instance: MonitoringService
  private isInitialized = false

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService()
    }
    return MonitoringService.instance
  }

  async initialize() {
    if (this.isInitialized) return

    try {
      // Initialize Sentry for error tracking
      if (config.monitoring.sentryDsn && typeof window !== 'undefined') {
        const Sentry = await import('@sentry/nextjs')
        
        Sentry.init({
          dsn: config.monitoring.sentryDsn,
          environment: config.app.environment,
          tracesSampleRate: config.app.environment === 'production' ? 0.1 : 1.0,
          debug: config.app.environment === 'development',
          beforeSend(event) {
            // Filter out development errors
            if (config.app.environment === 'development') {
              return null
            }
            return event
          },
        })
      }

      // Initialize Google Analytics
      if (config.monitoring.googleAnalyticsId && typeof window !== 'undefined') {
        const script = document.createElement('script')
        script.src = `https://www.googletagmanager.com/gtag/js?id=${config.monitoring.googleAnalyticsId}`
        script.async = true
        document.head.appendChild(script)

        window.dataLayer = window.dataLayer || []
        function gtag(...args: any[]) {
          window.dataLayer.push(args)
        }
        gtag('js', new Date())
        gtag('config', config.monitoring.googleAnalyticsId)
      }

      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize monitoring:', error)
    }
  }

  // Track custom events
  trackEvent(eventName: string, properties?: Record<string, any>) {
    try {
      // Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, properties)
      }

      // Custom analytics
      if (config.features.analytics) {
        console.log('Analytics Event:', { eventName, properties })
      }
    } catch (error) {
      console.error('Failed to track event:', error)
    }
  }

  // Track page views
  trackPageView(path: string, title?: string) {
    try {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('config', config.monitoring.googleAnalyticsId, {
          page_path: path,
          page_title: title,
        })
      }
    } catch (error) {
      console.error('Failed to track page view:', error)
    }
  }

  // Track user interactions
  trackUserInteraction(action: string, category: string, label?: string, value?: number) {
    this.trackEvent('user_interaction', {
      event_category: category,
      event_label: label,
      value: value,
      action: action,
    })
  }

  // Track performance metrics
  trackPerformance(metric: string, value: number, unit: string = 'ms') {
    this.trackEvent('performance_metric', {
      metric_name: metric,
      metric_value: value,
      metric_unit: unit,
    })
  }

  // Track errors
  trackError(error: Error, context?: Record<string, any>) {
    try {
      if (typeof window !== 'undefined') {
        const Sentry = require('@sentry/nextjs')
        Sentry.captureException(error, {
          extra: context,
        })
      }

      // Also log to console in development
      if (config.app.environment === 'development') {
        console.error('Tracked Error:', error, context)
      }
    } catch (trackingError) {
      console.error('Failed to track error:', trackingError)
    }
  }

  // Track business metrics
  trackBusinessMetric(metric: string, value: number, metadata?: Record<string, any>) {
    this.trackEvent('business_metric', {
      metric_name: metric,
      metric_value: value,
      ...metadata,
    })
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics: Map<string, number> = new Map()

  static startTiming(label: string): void {
    this.metrics.set(label, performance.now())
  }

  static endTiming(label: string): number {
    const startTime = this.metrics.get(label)
    if (!startTime) {
      console.warn(`No start time found for label: ${label}`)
      return 0
    }

    const duration = performance.now() - startTime
    this.metrics.delete(label)

    // Track performance metric
    MonitoringService.getInstance().trackPerformance(label, duration)

    return duration
  }

  static measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.startTiming(label)
    return fn().finally(() => {
      this.endTiming(label)
    })
  }

  static measureSync<T>(label: string, fn: () => T): T {
    this.startTiming(label)
    try {
      return fn()
    } finally {
      this.endTiming(label)
    }
  }
}

// User behavior tracking
export class UserBehaviorTracker {
  private static sessionStart = Date.now()
  private static interactions: Array<{ action: string; timestamp: number; element?: string }> = []

  static trackClick(element: string, metadata?: Record<string, any>) {
    this.interactions.push({
      action: 'click',
      timestamp: Date.now(),
      element,
    })

    MonitoringService.getInstance().trackUserInteraction('click', 'ui', element)
  }

  static trackNavigation(from: string, to: string) {
    MonitoringService.getInstance().trackUserInteraction('navigation', 'routing', `${from} -> ${to}`)
  }

  static trackFormSubmission(formName: string, success: boolean) {
    MonitoringService.getInstance().trackUserInteraction(
      'form_submission',
      'forms',
      formName,
      success ? 1 : 0
    )
  }

  static trackSearch(query: string, resultsCount: number) {
    MonitoringService.getInstance().trackUserInteraction('search', 'discovery', query, resultsCount)
  }

  static trackPurchase(productId: string, amount: number, currency: string = 'USD') {
    MonitoringService.getInstance().trackEvent('purchase', {
      product_id: productId,
      value: amount,
      currency: currency,
    })
  }

  static getSessionDuration(): number {
    return Date.now() - this.sessionStart
  }

  static getInteractionCount(): number {
    return this.interactions.length
  }
}

// Error boundary integration
export function withErrorTracking<T extends (...args: any[]) => any>(fn: T): T {
  return ((...args: any[]) => {
    try {
      const result = fn(...args)
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.catch((error) => {
          MonitoringService.getInstance().trackError(error, {
            function: fn.name,
            arguments: args,
          })
          throw error
        })
      }
      
      return result
    } catch (error) {
      MonitoringService.getInstance().trackError(error as Error, {
        function: fn.name,
        arguments: args,
      })
      throw error
    }
  }) as T
}

// React hook for monitoring
export function useMonitoring() {
  const monitoring = MonitoringService.getInstance()

  return {
    trackEvent: monitoring.trackEvent.bind(monitoring),
    trackPageView: monitoring.trackPageView.bind(monitoring),
    trackUserInteraction: monitoring.trackUserInteraction.bind(monitoring),
    trackPerformance: monitoring.trackPerformance.bind(monitoring),
    trackError: monitoring.trackError.bind(monitoring),
    trackBusinessMetric: monitoring.trackBusinessMetric.bind(monitoring),
  }
}

// Initialize monitoring on app start
if (typeof window !== 'undefined') {
  MonitoringService.getInstance().initialize()
}

// Global error handler
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    MonitoringService.getInstance().trackError(event.error, {
      type: 'global_error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    })
  })

  window.addEventListener('unhandledrejection', (event) => {
    MonitoringService.getInstance().trackError(new Error(event.reason), {
      type: 'unhandled_promise_rejection',
    })
  })
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance()

// Type declarations for global objects
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}
