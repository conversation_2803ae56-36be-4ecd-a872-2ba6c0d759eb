"use client"

import { ReactNode } from "react"
import { LucideIcon } from "lucide-react"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "danger" | "success" | "warning"
  size?: "sm" | "md" | "lg"
  isLoading?: boolean
  icon?: LucideIcon
  iconPosition?: "left" | "right"
  children: ReactNode
}

export default function Button({
  variant = "primary",
  size = "md",
  isLoading = false,
  icon: Icon,
  iconPosition = "left",
  children,
  className = "",
  disabled,
  ...props
}: ButtonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case "primary":
        return "bg-purple-600 hover:bg-purple-700 text-white border-purple-600/50"
      case "secondary":
        return "bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600/50"
      case "danger":
        return "bg-red-600 hover:bg-red-700 text-white border-red-600/50"
      case "success":
        return "bg-green-600 hover:bg-green-700 text-white border-green-600/50"
      case "warning":
        return "bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600/50"
      default:
        return "bg-purple-600 hover:bg-purple-700 text-white border-purple-600/50"
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "px-3 py-2 text-sm"
      case "md":
        return "px-4 py-3 text-base"
      case "lg":
        return "px-6 py-4 text-lg"
      default:
        return "px-4 py-3 text-base"
    }
  }

  const isDisabled = disabled || isLoading

  return (
    <button
      className={`
        inline-flex items-center justify-center space-x-2 space-x-reverse
        rounded-xl border font-medium transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-purple-500/50
        disabled:opacity-50 disabled:cursor-not-allowed
        ${getVariantClasses()}
        ${getSizeClasses()}
        ${className}
      `}
      disabled={isDisabled}
      {...props}
    >
      {isLoading ? (
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
      ) : (
        <>
          {Icon && iconPosition === "left" && <Icon className="w-5 h-5" />}
          <span>{children}</span>
          {Icon && iconPosition === "right" && <Icon className="w-5 h-5" />}
        </>
      )}
    </button>
  )
}

// Specialized button variants for common use cases
export function PrimaryButton(props: Omit<ButtonProps, "variant">) {
  return <Button variant="primary" {...props} />
}

export function SecondaryButton(props: Omit<ButtonProps, "variant">) {
  return <Button variant="secondary" {...props} />
}

export function DangerButton(props: Omit<ButtonProps, "variant">) {
  return <Button variant="danger" {...props} />
}

export function SuccessButton(props: Omit<ButtonProps, "variant">) {
  return <Button variant="success" {...props} />
}

export function WarningButton(props: Omit<ButtonProps, "variant">) {
  return <Button variant="warning" {...props} />
}
