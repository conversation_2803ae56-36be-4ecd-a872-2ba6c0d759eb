"use client"

import { useState, useMemo } from "react"
import ProductCard from "./ProductCard"
import Pagination from "./Pagination"
import type { Product } from "../types"

interface PaginatedProductSectionProps {
  title: string
  products: Product[]
  userRole: string
  productsPerPage?: number
  className?: string
}

export default function PaginatedProductSection({
  title,
  products,
  userRole,
  productsPerPage = 20,
  className = ""
}: PaginatedProductSectionProps) {
  const [currentPage, setCurrentPage] = useState(1)

  const { paginatedProducts, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    const paginatedProducts = products.slice(startIndex, endIndex)
    const totalPages = Math.ceil(products.length / productsPerPage)

    return { paginatedProducts, totalPages }
  }, [products, currentPage, productsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top of section when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (products.length === 0) return null

  return (
    <section className={`mb-8 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        <div className="text-sm text-gray-400">
          {products.length} منتج
        </div>
      </div>

      {/* Product Grid - 3 cards per row */}
      <div className="grid grid-cols-3 gap-4 mb-8">
        {paginatedProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            userRole={userRole}
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-8"
        />
      )}
    </section>
  )
}
