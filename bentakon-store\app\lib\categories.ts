import { supabase } from './supabase'
import { validateData, sanitizeObject } from './validations'
import { z } from 'zod'

// Category type definition
export interface Category {
  id: string
  tenant_id: string
  name?: string
  slug: string
  description?: string
  image: string
  created_at: string
  updated_at: string
  product_count?: number // Virtual field for UI display
}

// Category validation schema
export const categorySchema = z.object({
  id: z.string().optional(),
  tenant_id: z.string().uuid('معرف المستأجر غير صالح').optional(),
  name: z.string()
    .max(100, 'اسم الفئة لا يمكن أن يزيد عن 100 حرف')
    .regex(/^[a-zA-Z0-9\u0600-\u06FF\s\-_]*$/, 'اسم الفئة يحتوي على أحرف غير مسموحة')
    .optional(),
  slug: z.string()
    .min(1, 'الرابط المختصر مطلوب')
    .max(100, 'الرابط المختصر لا يمكن أن يزيد عن 100 حرف')
    .regex(/^[a-z0-9\-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط (مثل: name-name-name)'),
  description: z.string()
    .max(500, 'الوصف لا يمكن أن يزيد عن 500 حرف')
    .optional(),
  image: z.string()
    .url('رابط الصورة غير صالح')
    .min(1, 'صورة الفئة مطلوبة')
})

export const categoryCreateSchema = categorySchema.omit({ id: true, tenant_id: true })
export const categoryUpdateSchema = categorySchema.partial().omit({ id: true, tenant_id: true })

// Helper function to generate slug from name
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    // Replace Arabic characters with transliteration
    .replace(/[أإآا]/g, 'a')
    .replace(/[ب]/g, 'b')
    .replace(/[ت]/g, 't')
    .replace(/[ث]/g, 'th')
    .replace(/[ج]/g, 'j')
    .replace(/[ح]/g, 'h')
    .replace(/[خ]/g, 'kh')
    .replace(/[د]/g, 'd')
    .replace(/[ذ]/g, 'dh')
    .replace(/[ر]/g, 'r')
    .replace(/[ز]/g, 'z')
    .replace(/[س]/g, 's')
    .replace(/[ش]/g, 'sh')
    .replace(/[ص]/g, 's')
    .replace(/[ض]/g, 'd')
    .replace(/[ط]/g, 't')
    .replace(/[ظ]/g, 'z')
    .replace(/[ع]/g, 'a')
    .replace(/[غ]/g, 'gh')
    .replace(/[ف]/g, 'f')
    .replace(/[ق]/g, 'q')
    .replace(/[ك]/g, 'k')
    .replace(/[ل]/g, 'l')
    .replace(/[م]/g, 'm')
    .replace(/[ن]/g, 'n')
    .replace(/[ه]/g, 'h')
    .replace(/[و]/g, 'w')
    .replace(/[ي]/g, 'y')
    // Replace spaces with hyphens and clean up
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9\-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
}

// Get all categories for a tenant
export async function getCategories(tenantId: string): Promise<{ success: boolean; data?: Category[]; error?: string }> {
  try {
    console.log('Fetching categories for tenant:', tenantId)

    // Use API endpoint instead of direct database access
    const response = await fetch('/api/categories', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.warn(`Categories API returned ${response.status}`)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('Categories API response:', result)

    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch categories')
    }

    return {
      success: true,
      data: result.data || []
    }
  } catch (error) {
    console.error('Error fetching categories from API:', error)
    return {
      success: false,
      error: 'حدث خطأ في جلب الفئات'
    }
  }
}

// Get single category by ID
export async function getCategory(id: string, tenantId: string): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single()

    if (error) {
      console.error('Error fetching category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in getCategory:', error)
    return { success: false, error: 'حدث خطأ في جلب الفئة' }
  }
}

// Create new category
export async function createCategory(
  categoryData: z.infer<typeof categoryCreateSchema>, 
  tenantId: string
): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    // Sanitize and validate input
    const sanitizedData = sanitizeObject(categoryData)
    const validation = validateData(categoryCreateSchema, sanitizedData)
    
    if (!validation.success) {
      return { success: false, error: validation.errors.join(', ') }
    }

    const validatedData = validation.data

    // Auto-generate slug if not provided and name exists
    if (!validatedData.slug && validatedData.name) {
      validatedData.slug = generateSlug(validatedData.name)
    }

    // Check for duplicate name in tenant (only if name is provided)
    if (validatedData.name) {
      const { data: existingByName } = await supabase
        .from('categories')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('name', validatedData.name)
        .single()

      if (existingByName) {
        return { success: false, error: 'اسم الفئة موجود بالفعل' }
      }
    }

    // Check for duplicate slug in tenant
    const { data: existingBySlug } = await supabase
      .from('categories')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('slug', validatedData.slug)
      .single()

    if (existingBySlug) {
      return { success: false, error: 'الرابط المختصر موجود بالفعل' }
    }

    // Insert new category
    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...validatedData,
        tenant_id: tenantId
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in createCategory:', error)
    return { success: false, error: 'حدث خطأ في إنشاء الفئة' }
  }
}

// Update existing category
export async function updateCategory(
  id: string,
  categoryData: z.infer<typeof categoryUpdateSchema>,
  tenantId: string
): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    // Sanitize and validate input
    const sanitizedData = sanitizeObject(categoryData)
    const validation = validateData(categoryUpdateSchema, sanitizedData)
    
    if (!validation.success) {
      return { success: false, error: validation.errors.join(', ') }
    }

    const validatedData = validation.data

    // Auto-generate slug if name is being updated but slug is not provided
    if (validatedData.name && !validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name)
    }

    // Check for duplicate name in tenant (excluding current category)
    if (validatedData.name) {
      const { data: existingByName } = await supabase
        .from('categories')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('name', validatedData.name)
        .neq('id', id)
        .single()

      if (existingByName) {
        return { success: false, error: 'اسم الفئة موجود بالفعل' }
      }
    }

    // Check for duplicate slug in tenant (excluding current category)
    if (validatedData.slug) {
      const { data: existingBySlug } = await supabase
        .from('categories')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('slug', validatedData.slug)
        .neq('id', id)
        .single()

      if (existingBySlug) {
        return { success: false, error: 'الرابط المختصر موجود بالفعل' }
      }
    }

    // Update category
    const { data, error } = await supabase
      .from('categories')
      .update(validatedData)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('Error updating category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in updateCategory:', error)
    return { success: false, error: 'حدث خطأ في تحديث الفئة' }
  }
}

// Delete category
export async function deleteCategory(id: string, tenantId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if category has products
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id')
      .eq('category_id', id)
      .eq('tenant_id', tenantId)
      .limit(1)

    if (productsError) {
      console.error('Error checking products:', productsError)
      return { success: false, error: 'حدث خطأ في التحقق من المنتجات' }
    }

    if (products && products.length > 0) {
      return { success: false, error: 'لا يمكن حذف الفئة لأنها تحتوي على منتجات' }
    }

    // Delete category
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id)
      .eq('tenant_id', tenantId)

    if (error) {
      console.error('Error deleting category:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deleteCategory:', error)
    return { success: false, error: 'حدث خطأ في حذف الفئة' }
  }
}


