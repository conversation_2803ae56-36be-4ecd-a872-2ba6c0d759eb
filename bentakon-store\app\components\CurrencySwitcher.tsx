'use client'

import { useData } from '../contexts/DataContext'
import { ChevronDown } from 'lucide-react'
import { getCurrencySymbol } from '../utils/currency'

export function CurrencySwitcher() {
  const { currencies, selectedCurrency, setSelectedCurrency } = useData()

  if (currencies.length <= 1) {
    return null
  }

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    e.stopPropagation() // Prevent event bubbling
    setSelectedCurrency(e.target.value)
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent event bubbling
  }

  return (
    <div className="relative" onClick={handleClick}>
      <select
        value={selectedCurrency}
        onChange={handleCurrencyChange}
        onClick={handleClick}
        className="appearance-none bg-gray-800 text-white px-3 py-2 pr-8 rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none text-sm w-full"
      >
        {currencies.map((currency) => (
          <option key={currency.code} value={currency.code}>
            {getCurrencySymbol(currency.code)} {currency.code}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  )
}
