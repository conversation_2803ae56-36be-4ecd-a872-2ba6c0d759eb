/**
 * Centralized caching utility for the application
 * Provides in-memory caching with TTL support and cache invalidation
 */

import { cacheConfig } from './config'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  tags: string[]
}

interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  size: number
}

class CacheManager {
  private cache = new Map<string, CacheEntry<any>>()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0
  }

  /**
   * Get item from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      this.stats.deletes++
      this.stats.misses++
      return null
    }

    this.stats.hits++
    return entry.data
  }

  /**
   * Set item in cache
   */
  set<T>(key: string, data: T, ttl?: number, tags: string[] = []): void {
    const defaultTtl = cacheConfig.durations.static * 1000 // Convert to ms
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || defaultTtl,
      tags
    })
    
    this.stats.sets++
    this.stats.size = this.cache.size
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
      this.stats.size = this.cache.size
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.stats.deletes += this.stats.size
    this.stats.size = 0
  }

  /**
   * Invalidate cache entries by tag
   */
  invalidateByTag(tag: string): number {
    let deleted = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.includes(tag)) {
        this.cache.delete(key)
        deleted++
      }
    }
    
    this.stats.deletes += deleted
    this.stats.size = this.cache.size
    return deleted
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidateByPattern(pattern: RegExp): number {
    let deleted = 0
    
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key)
        deleted++
      }
    }
    
    this.stats.deletes += deleted
    this.stats.size = this.cache.size
    return deleted
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats & { hitRate: number } {
    const total = this.stats.hits + this.stats.misses
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
    
    return {
      ...this.stats,
      hitRate
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    let deleted = 0
    const now = Date.now()
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        deleted++
      }
    }
    
    this.stats.deletes += deleted
    this.stats.size = this.cache.size
    return deleted
  }

  /**
   * Get or set with a factory function
   */
  async getOrSet<T>(
    key: string, 
    factory: () => Promise<T>, 
    ttl?: number, 
    tags: string[] = []
  ): Promise<T> {
    const cached = this.get<T>(key)
    
    if (cached !== null) {
      return cached
    }
    
    const data = await factory()
    this.set(key, data, ttl, tags)
    return data
  }

  /**
   * Memoize a function with caching
   */
  memoize<TArgs extends any[], TReturn>(
    fn: (...args: TArgs) => Promise<TReturn>,
    keyGenerator: (...args: TArgs) => string,
    ttl?: number,
    tags: string[] = []
  ) {
    return async (...args: TArgs): Promise<TReturn> => {
      const key = keyGenerator(...args)
      return this.getOrSet(key, () => fn(...args), ttl, tags)
    }
  }
}

// Create singleton instance
const cacheManager = new CacheManager()

// Auto cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheManager.cleanup()
  }, 5 * 60 * 1000)
}

export default cacheManager

/**
 * Utility functions for common caching patterns
 */
export const cacheUtils = {
  /**
   * Generate cache key for products
   */
  productKey: (tenantId: string, page: number = 1, filters: Record<string, any> = {}) => {
    const filterString = Object.entries(filters)
      .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      .map(([key, value]) => `${key}:${value}`)
      .sort()
      .join('|')
    return `products:${tenantId}:${page}:${filterString}`
  },

  /**
   * Generate cache key for orders
   */
  orderKey: (tenantId: string, userId?: string, page: number = 1, filters: Record<string, any> = {}) => {
    const filterString = Object.entries(filters)
      .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      .map(([key, value]) => `${key}:${value}`)
      .sort()
      .join('|')
    const userPart = userId ? `:${userId}` : ''
    return `orders:${tenantId}${userPart}:${page}:${filterString}`
  },

  /**
   * Generate cache key for earnings
   */
  earningsKey: (tenantId: string, period: number = 30) => {
    return `earnings:${tenantId}:${period}`
  },

  /**
   * Generate cache key for user profile
   */
  userProfileKey: (userId: string, tenantId: string) => {
    return `user_profile:${userId}:${tenantId}`
  },

  /**
   * Generate cache key for tenant data
   */
  tenantKey: (identifier: string) => {
    return `tenant:${identifier}`
  },

  /**
   * Get TTL for different data types
   */
  getTTL: (type: keyof typeof cacheConfig.durations) => {
    return cacheConfig.durations[type] * 1000 // Convert to milliseconds
  },

  /**
   * Invalidate all product-related cache
   */
  invalidateProducts: (tenantId: string) => {
    return cacheManager.invalidateByPattern(new RegExp(`^products:${tenantId}:`))
  },

  /**
   * Invalidate all order-related cache
   */
  invalidateOrders: (tenantId: string) => {
    return cacheManager.invalidateByPattern(new RegExp(`^orders:${tenantId}:`))
  },

  /**
   * Invalidate earnings cache
   */
  invalidateEarnings: (tenantId: string) => {
    return cacheManager.invalidateByPattern(new RegExp(`^earnings:${tenantId}:`))
  }
}

/**
 * React hook for caching
 */
export function useCache() {
  return {
    get: cacheManager.get.bind(cacheManager),
    set: cacheManager.set.bind(cacheManager),
    delete: cacheManager.delete.bind(cacheManager),
    getOrSet: cacheManager.getOrSet.bind(cacheManager),
    invalidateByTag: cacheManager.invalidateByTag.bind(cacheManager),
    getStats: cacheManager.getStats.bind(cacheManager)
  }
}
