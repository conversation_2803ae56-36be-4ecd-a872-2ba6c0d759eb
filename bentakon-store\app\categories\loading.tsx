import { LoadingSpinner } from "../components/LoadingStates"

export default function CategoriesLoading() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 md:mb-8">
          <div>
            <div className="h-8 bg-gray-800 rounded-lg w-48 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-800 rounded-lg w-64 animate-pulse"></div>
          </div>
          <div className="h-12 bg-gray-800 rounded-lg w-full sm:w-80 animate-pulse"></div>
        </div>

        {/* Categories Grid Skeleton */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700/30 h-full flex flex-col">
              {/* Image skeleton */}
              <div className="aspect-square bg-gray-700 animate-pulse"></div>
              {/* Content skeleton */}
              <div className="aspect-[2/1] p-3 flex flex-col justify-center">
                <div className="h-4 bg-gray-700 rounded w-3/4 mx-auto mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-700 rounded w-1/2 mx-auto animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Loading indicator */}
        <div className="flex items-center justify-center mt-12">
          <div className="text-center">
            <LoadingSpinner size="lg" className="text-purple-500 mx-auto mb-4" />
            <p className="text-gray-400">جاري تحميل الفئات...</p>
          </div>
        </div>
      </div>
    </div>
  )
}
