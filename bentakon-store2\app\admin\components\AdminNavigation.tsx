"use client"

import { <PERSON>u, <PERSON> } from "lucide-react"
import { LucideIcon } from "lucide-react"

interface Tab {
  id: string
  label: string
  icon: LucideIcon
}

interface AdminNavigationProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  isMobileMenuOpen: boolean
  onMobileMenuToggle: () => void
}

export default function AdminNavigation({
  tabs,
  activeTab,
  onTabChange,
  isMobileMenuOpen,
  onMobileMenuToggle
}: AdminNavigationProps) {
  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden mb-6">
        <button
          onClick={onMobileMenuToggle}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-gray-800/50 rounded-xl border border-gray-700/50"
        >
          <Menu className="w-5 h-5" />
          <span>القائمة</span>
        </button>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden lg:flex bg-gray-800/30 backdrop-blur-md rounded-xl p-2 border border-gray-700/50 mb-8">
        {tabs.map((tab) => {
          const Icon = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center space-x-2 space-x-reverse px-4 py-3 rounded-lg transition-all duration-200 flex-1 justify-center ${
                activeTab === tab.id
                  ? "bg-purple-600 text-white shadow-lg"
                  : "text-gray-400 hover:text-white hover:bg-gray-700/50"
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="font-medium">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Mobile Navigation Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
          <div className="bg-gray-900/95 backdrop-blur-md w-80 h-full border-l border-gray-700/50 shadow-2xl">
            <div className="p-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-xl font-bold">لوحة التحكم</h2>
                <button
                  onClick={onMobileMenuToggle}
                  className="p-2 text-gray-400 hover:text-white rounded-xl hover:bg-gray-700/50"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => {
                        onTabChange(tab.id)
                        onMobileMenuToggle()
                      }}
                      className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-xl transition-all duration-200 ${
                        activeTab === tab.id
                          ? "bg-purple-600 text-white"
                          : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
