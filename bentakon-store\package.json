{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3002", "lint": "next lint", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@sentry/nextjs": "^9.40.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@types/dompurify": "^3.0.5", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "dompurify": "^3.2.6", "embla-carousel-react": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "latest", "recharts": "latest", "sanitize-html": "^2.17.0", "sonner": "^2.0.6", "swiper": "^11.2.10", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/sanitize-html": "^2.16.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}