import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { z } from 'zod'
import sanitizeHtml from 'sanitize-html'
import { cacheInvalidation } from '../../../../lib/optimizedQueries'

// Enhanced rate limiting with IP tracking - more lenient for development
const rateLimit = (() => {
  const requests = new Map()
  const blockedIPs = new Set()

  return (identifier: string, limit: number) => {
    // Skip rate limiting in development
    if (process.env.NODE_ENV === 'development') {
      return true
    }

    const now = Date.now()
    const windowStart = now - 60000 // 1 minute window

    // Check if IP is temporarily blocked
    if (blockedIPs.has(identifier)) {
      return false
    }

    if (!requests.has(identifier)) {
      requests.set(identifier, [])
    }

    const userRequests = requests.get(identifier)
    const recentRequests = userRequests.filter((time: number) => time > windowStart)

    if (recentRequests.length >= limit) {
      // Block IP for 2 minutes if they exceed rate limit (reduced from 5)
      blockedIPs.add(identifier)
      setTimeout(() => blockedIPs.delete(identifier), 2 * 60 * 1000)
      return false
    }

    recentRequests.push(now)
    requests.set(identifier, recentRequests)
    return true
  }
})()

// Audit logging for security events - reduced noise in development
const auditLog = (action: string, userId: string, tenantId: string, details: any = {}) => {
  // Only log security-critical events in development
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isSecurityEvent = action.includes('ERROR') || action.includes('UNAUTHORIZED') || action.includes('FORBIDDEN')

  if (!isDevelopment || isSecurityEvent) {
    console.log(`[AUDIT] ${new Date().toISOString()} - ${action}`, {
      userId,
      tenantId,
      ...details,
      ip: details.ip || 'unknown'
    })
  }
}

// Valid operations enum for type safety
const VALID_OPERATIONS = ['create', 'update', 'delete', 'get', 'list'] as const
type ValidOperation = typeof VALID_OPERATIONS[number]

// Validation schemas
const productCreateSchema = z.object({
  title: z.string().min(1, "عنوان المنتج مطلوب").max(200, "عنوان المنتج لا يمكن أن يزيد عن 200 حرف"),
  description: z.string().min(1, "وصف المنتج مطلوب"),
  cover_image: z.string().url("رابط الصورة غير صالح").optional().or(z.literal("")),
  category_id: z.string().optional(),
  tags: z.array(z.string()).default([]),
  featured: z.boolean().default(false),
  packages: z.array(z.any()).default([]),
  customFields: z.array(z.any()).default([]),
  // Product-level pricing
  original_price: z.number().min(0, "السعر الأصلي لا يمكن أن يكون سالباً").optional(),
  user_price: z.number().min(0, "سعر المستخدم لا يمكن أن يكون سالباً").optional(),
  discount_price: z.number().min(0, "سعر الخصم لا يمكن أن يكون سالباً").optional(),
  distributor_price: z.number().min(0, "سعر الموزع لا يمكن أن يكون سالباً").optional()
})

const productUpdateSchema = productCreateSchema.partial()

// Enhanced security validation
async function validateTenantAndUser(supabase: any, userId: string) {
  const { data: profile, error: profileError } = await supabase
    .from('user_profiles')
    .select('tenant_id, role, name, email')
    .eq('id', userId)
    .single()

  if (profileError || !profile) {
    throw new Error('User profile not found')
  }

  if (!['admin', 'owner'].includes(profile.role)) {
    throw new Error('Insufficient permissions')
  }

  if (!profile.tenant_id) {
    throw new Error('User not associated with any tenant')
  }

  // Verify tenant exists and is active
  const { data: tenant, error: tenantError } = await supabase
    .from('tenants')
    .select('id, name, status, is_active')
    .eq('id', profile.tenant_id)
    .single()

  if (tenantError || !tenant || (tenant.status !== 'active' && !tenant.is_active)) {
    throw new Error('Invalid or inactive tenant')
  }

  return { profile, tenant }
}

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
  // Try various headers for IP detection
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (realIP) {
    return realIP
  }
  if (cfConnectingIP) {
    return cfConnectingIP
  }

  return request.ip || 'localhost'
}

// Unified handler for all product operations
export async function POST(request: NextRequest) {
  const identifier = getClientIP(request)
  let userId: string | null = null
  let tenantId: string | null = null

  try {
    // Enhanced rate limiting - more lenient limit for authenticated admin users
    if (!rateLimit(identifier, 50)) {
      auditLog('RATE_LIMIT_EXCEEDED', 'unknown', 'unknown', { ip: identifier })
      return NextResponse.json({ error: 'Too many requests. Please try again later.' }, { status: 429 })
    }

    // Validate request content type
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }

    const supabase = await createClient()

    // Enhanced authentication check
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      auditLog('UNAUTHORIZED_ACCESS', 'unknown', 'unknown', { ip: identifier })
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    userId = user.id

    // Validate tenant and user permissions
    const { profile, tenant } = await validateTenantAndUser(supabase, user.id)
    tenantId = profile.tenant_id

    // Parse and validate request body
    let body: any
    try {
      body = await request.json()
    } catch (parseError) {
      auditLog('INVALID_JSON', userId, tenantId, { ip: identifier })
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
    }

    const { operation, data, id } = body

    // Validate operation type
    if (!operation || !VALID_OPERATIONS.includes(operation as ValidOperation)) {
      auditLog('INVALID_OPERATION', userId, tenantId, { operation, ip: identifier })
      return NextResponse.json({ error: 'Invalid or missing operation' }, { status: 400 })
    }

    // Log the operation attempt
    auditLog(`OPERATION_${operation.toUpperCase()}_ATTEMPT`, userId, tenantId, {
      ip: identifier,
      productId: id,
      hasData: !!data
    })

    // Route to appropriate handler
    switch (operation as ValidOperation) {
      case 'create':
        return await handleCreate(supabase, profile, data, identifier)
      case 'update':
        return await handleUpdate(supabase, profile, data, id, identifier)
      case 'delete':
        return await handleDelete(supabase, profile, id, identifier)
      case 'get':
        return await handleGet(supabase, profile, id, identifier)
      case 'list':
        return await handleList(supabase, profile, data, identifier)
      default:
        return NextResponse.json({ error: 'Operation not implemented' }, { status: 501 })
    }

  } catch (error) {
    // Enhanced error logging with security context
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    auditLog('API_ERROR', userId || 'unknown', tenantId || 'unknown', {
      error: errorMessage,
      ip: identifier,
      stack: error instanceof Error ? error.stack : undefined
    })

    console.error('Error in unified products API:', {
      error: errorMessage,
      userId,
      tenantId,
      ip: identifier,
      timestamp: new Date().toISOString()
    })

    // Handle specific error types
    if (error instanceof z.ZodError) {
      return createSecureResponse({
        error: 'بيانات غير صالحة',
        details: error.errors.map(e => e.message).join(', ')
      }, 400)
    }

    // Don't expose internal error details in production
    const isProduction = process.env.NODE_ENV === 'production'
    return createSecureResponse({
      error: 'Internal server error',
      details: isProduction ? 'An unexpected error occurred' : errorMessage
    }, 500)
  }
}

// Helper function to create secure responses with proper headers
function createSecureResponse(data: any, status: number = 200) {
  const response = NextResponse.json(data, { status })

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  response.headers.set('Pragma', 'no-cache')
  response.headers.set('Expires', '0')

  return response
}

// Create product with enhanced security
async function handleCreate(supabase: any, profile: any, data: any, ip: string) {
  // Validate input data exists
  if (!data || typeof data !== 'object') {
    auditLog('CREATE_INVALID_DATA', profile.id, profile.tenant_id, { ip })
    return createSecureResponse({ error: 'Invalid or missing product data' }, 400)
  }

  // Enhanced data sanitization
  const sanitizedData = {
    ...data,
    title: data.title ? sanitizeHtml(data.title.toString().trim()) : '',
    description: data.description ? sanitizeHtml(data.description.toString().trim()) : '',
    // Ensure numeric fields are properly typed
    original_price: data.original_price ? Number(data.original_price) : undefined,
    user_price: data.user_price ? Number(data.user_price) : undefined,
    discount_price: data.discount_price ? Number(data.discount_price) : undefined,
    distributor_price: data.distributor_price ? Number(data.distributor_price) : undefined
  }

  // Validate against schema
  let validatedData
  try {
    validatedData = productCreateSchema.parse(sanitizedData)
  } catch (validationError) {
    auditLog('CREATE_VALIDATION_FAILED', profile.id, profile.tenant_id, {
      ip,
      error: validationError instanceof Error ? validationError.message : 'Unknown validation error'
    })
    throw validationError
  }

  // Generate secure slug from title
  const slug = validatedData.title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
    .substring(0, 100) // Limit slug length

  if (!slug) {
    return createSecureResponse({ error: 'Invalid product title' }, 400)
  }

  // Check if slug already exists within tenant (secure query)
  const { data: existingProduct, error: checkError } = await supabase
    .from('products')
    .select('id')
    .eq('slug', slug)
    .eq('tenant_id', profile.tenant_id)
    .maybeSingle()

  if (checkError) {
    auditLog('CREATE_DB_ERROR', profile.id, profile.tenant_id, { ip, error: checkError.message })
    throw new Error('Database error during slug validation')
  }

  if (existingProduct) {
    auditLog('CREATE_DUPLICATE_SLUG', profile.id, profile.tenant_id, { ip, slug })
    return createSecureResponse({ error: 'منتج بهذا الاسم موجود بالفعل' }, 409)
  }

  // Create product with transaction safety
  const { data: product, error: productError } = await supabase
    .from('products')
    .insert({
      tenant_id: profile.tenant_id, // CRITICAL: Always set tenant_id
      slug,
      title: validatedData.title,
      description: validatedData.description,
      cover_image: validatedData.cover_image || null,
      category_id: validatedData.category_id || null,
      tags: validatedData.tags || [],
      featured: validatedData.featured || false,
      original_price: validatedData.original_price || null,
      user_price: validatedData.user_price || null,
      discount_price: validatedData.discount_price || null,
      distributor_price: validatedData.distributor_price || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (productError) {
    auditLog('CREATE_PRODUCT_FAILED', profile.id, profile.tenant_id, {
      ip,
      error: productError.message,
      slug
    })
    throw new Error(`Failed to create product: ${productError.message}`)
  }

  // Handle packages if provided
  if (validatedData.packages && validatedData.packages.length > 0) {
    const packagesData = validatedData.packages.map((pkg: any) => ({
      ...pkg,
      product_id: product.id,
      tenant_id: profile.tenant_id
    }))

    const { error: packagesError } = await supabase
      .from('packages')
      .insert(packagesData)

    if (packagesError) {
      console.error('Error creating packages:', packagesError)
    }
  }

  // Handle custom fields if provided
  if (validatedData.customFields && validatedData.customFields.length > 0) {
    const customFieldsData = validatedData.customFields.map((field: any, index: number) => ({
      tenant_id: profile.tenant_id,
      product_id: product.id,
      label: field.label,
      field_type: field.field_type || 'text',
      description: field.description,
      required: field.required || false,
      placeholder: field.placeholder,
      field_order: index + 1,
      validation_rules: field.options ? { options: field.options } : null
    }))

    const { error: fieldsError } = await supabase
      .from('custom_fields')
      .insert(customFieldsData)

    if (fieldsError) {
      console.error('Error creating custom fields:', fieldsError)
    }
  }

  // Log successful creation
  auditLog('CREATE_SUCCESS', profile.id, profile.tenant_id, {
    ip,
    productId: product.id,
    productTitle: product.title
  })

  // Invalidate cache after successful creation
  cacheInvalidation.onProductChange(profile.tenant_id)

  return createSecureResponse({
    success: true,
    product,
    message: 'تم إنشاء المنتج بنجاح'
  }, 201)
}

// Update product with enhanced security
async function handleUpdate(supabase: any, profile: any, data: any, id: string, ip: string) {
  // Validate input
  if (!id || typeof id !== 'string' || id.trim().length === 0) {
    auditLog('UPDATE_INVALID_ID', profile.id, profile.tenant_id, { ip })
    return createSecureResponse({ error: 'Valid product ID is required' }, 400)
  }

  if (!data || typeof data !== 'object') {
    auditLog('UPDATE_INVALID_DATA', profile.id, profile.tenant_id, { ip, productId: id })
    return createSecureResponse({ error: 'Invalid or missing update data' }, 400)
  }

  const sanitizedData = {
    ...data,
    title: data.title ? sanitizeHtml(data.title) : undefined,
    description: data.description ? sanitizeHtml(data.description) : undefined
  }

  const validatedData = productUpdateSchema.parse(sanitizedData)

  // Check if product exists and belongs to tenant
  const { data: existingProduct } = await supabase
    .from('products')
    .select('id, slug')
    .eq('id', id)
    .eq('tenant_id', profile.tenant_id)
    .single()

  if (!existingProduct) {
    return NextResponse.json({ error: 'المنتج غير موجود' }, { status: 404 })
  }

  // Generate new slug if title changed
  let slug = existingProduct.slug
  if (validatedData.title) {
    slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Check if new slug conflicts with existing products
    if (slug !== existingProduct.slug) {
      const { data: conflictingProduct } = await supabase
        .from('products')
        .select('id')
        .eq('slug', slug)
        .eq('tenant_id', profile.tenant_id)
        .neq('id', id)
        .single()

      if (conflictingProduct) {
        return NextResponse.json({ error: 'منتج بهذا الاسم موجود بالفعل' }, { status: 409 })
      }
    }
  }

  // Update product
  const updateData = {
    ...validatedData,
    slug,
    updated_at: new Date().toISOString()
  }

  const { data: updatedProduct, error: updateError } = await supabase
    .from('products')
    .update(updateData)
    .eq('id', id)
    .eq('tenant_id', profile.tenant_id)
    .select()
    .single()

  if (updateError) {
    throw updateError
  }

  // Handle packages update if provided
  if (validatedData.packages !== undefined) {
    // Delete existing packages
    await supabase
      .from('packages')
      .delete()
      .eq('product_id', id)

    // Insert new packages
    if (validatedData.packages.length > 0) {
      const packagesData = validatedData.packages.map((pkg: any) => ({
        ...pkg,
        product_id: id,
        tenant_id: profile.tenant_id
      }))

      await supabase
        .from('packages')
        .insert(packagesData)
    }
  }

  // Handle custom fields update if provided
  if (validatedData.customFields !== undefined) {
    // Delete existing custom fields
    await supabase
      .from('custom_fields')
      .delete()
      .eq('product_id', id)

    // Insert new custom fields
    if (validatedData.customFields.length > 0) {
      const customFieldsData = validatedData.customFields.map((field: any, index: number) => ({
        tenant_id: profile.tenant_id,
        product_id: id,
        label: field.label,
        field_type: field.field_type || 'text',
        description: field.description,
        required: field.required || false,
        placeholder: field.placeholder,
        field_order: index + 1,
        validation_rules: field.options ? { options: field.options } : null
      }))

      await supabase
        .from('custom_fields')
        .insert(customFieldsData)
    }
  }

  // Log successful update
  auditLog('UPDATE_SUCCESS', profile.id, profile.tenant_id, {
    ip,
    productId: id,
    productTitle: updatedProduct.title
  })

  // Invalidate cache after successful update
  cacheInvalidation.onProductChange(profile.tenant_id)

  return createSecureResponse({
    success: true,
    product: updatedProduct,
    message: 'تم تحديث المنتج بنجاح'
  })
}

// Delete product with enhanced security
async function handleDelete(supabase: any, profile: any, id: string, ip: string) {
  // Validate input
  if (!id || typeof id !== 'string' || id.trim().length === 0) {
    auditLog('DELETE_INVALID_ID', profile.id, profile.tenant_id, { ip })
    return createSecureResponse({ error: 'Valid product ID is required' }, 400)
  }

  const productId = id.trim()

  // Check if product exists and belongs to tenant (secure query)
  const { data: existingProduct, error: checkError } = await supabase
    .from('products')
    .select('id, title, slug')
    .eq('id', productId)
    .eq('tenant_id', profile.tenant_id) // CRITICAL: Tenant isolation
    .maybeSingle()

  if (checkError) {
    auditLog('DELETE_DB_ERROR', profile.id, profile.tenant_id, {
      ip,
      productId,
      error: checkError.message
    })
    throw new Error('Database error during product validation')
  }

  if (!existingProduct) {
    auditLog('DELETE_NOT_FOUND', profile.id, profile.tenant_id, { ip, productId })
    return createSecureResponse({ error: 'المنتج غير موجود أو غير مصرح لك بحذفه' }, 404)
  }

  // SECURITY FIX: Delete related data with proper tenant isolation
  const deletePromises = [
    // Packages - include tenant_id for security
    supabase
      .from('packages')
      .delete()
      .eq('product_id', productId)
      .eq('tenant_id', profile.tenant_id),

    // Custom fields - include tenant_id for security
    supabase
      .from('custom_fields')
      .delete()
      .eq('product_id', productId)
      .eq('tenant_id', profile.tenant_id),

    // Dropdowns - include tenant_id for security
    supabase
      .from('dropdowns')
      .delete()
      .eq('product_id', productId)
      .eq('tenant_id', profile.tenant_id)
  ]

  try {
    const deleteResults = await Promise.all(deletePromises)

    // Check for errors in related data deletion
    for (const result of deleteResults) {
      if (result.error) {
        auditLog('DELETE_RELATED_DATA_FAILED', profile.id, profile.tenant_id, {
          ip,
          productId,
          error: result.error.message
        })
        throw new Error(`Failed to delete related data: ${result.error.message}`)
      }
    }
  } catch (relatedError) {
    auditLog('DELETE_RELATED_DATA_ERROR', profile.id, profile.tenant_id, {
      ip,
      productId,
      error: relatedError instanceof Error ? relatedError.message : 'Unknown error'
    })
    throw relatedError
  }

  // Delete the main product with tenant isolation
  const { error: deleteError } = await supabase
    .from('products')
    .delete()
    .eq('id', productId)
    .eq('tenant_id', profile.tenant_id) // CRITICAL: Double-check tenant isolation

  if (deleteError) {
    auditLog('DELETE_PRODUCT_FAILED', profile.id, profile.tenant_id, {
      ip,
      productId,
      error: deleteError.message
    })
    throw new Error(`Failed to delete product: ${deleteError.message}`)
  }

  // Log successful deletion
  auditLog('DELETE_SUCCESS', profile.id, profile.tenant_id, {
    ip,
    productId,
    productTitle: existingProduct.title
  })

  // Invalidate cache after successful deletion
  cacheInvalidation.onProductChange(profile.tenant_id)

  return createSecureResponse({
    success: true,
    message: 'تم حذف المنتج بنجاح'
  })
}

// Get single product with enhanced security
async function handleGet(supabase: any, profile: any, id: string, ip: string) {
  // Validate input
  if (!id || typeof id !== 'string' || id.trim().length === 0) {
    auditLog('GET_INVALID_ID', profile.id, profile.tenant_id, { ip })
    return createSecureResponse({ error: 'Valid product ID is required' }, 400)
  }

  const productId = id.trim()

  // Secure query with tenant isolation
  const { data: product, error } = await supabase
    .from('products')
    .select(`
      *,
      categories (
        id,
        name,
        slug
      ),
      packages (*),
      custom_fields (*)
    `)
    .eq('id', productId)
    .eq('tenant_id', profile.tenant_id) // CRITICAL: Tenant isolation
    .maybeSingle()

  if (error) {
    auditLog('GET_DB_ERROR', profile.id, profile.tenant_id, {
      ip,
      productId,
      error: error.message
    })
    throw new Error(`Database error: ${error.message}`)
  }

  if (!product) {
    auditLog('GET_NOT_FOUND', profile.id, profile.tenant_id, { ip, productId })
    return createSecureResponse({ error: 'المنتج غير موجود أو غير مصرح لك بالوصول إليه' }, 404)
  }

  auditLog('GET_SUCCESS', profile.id, profile.tenant_id, {
    ip,
    productId,
    productTitle: product.title
  })

  return createSecureResponse({
    success: true,
    product
  })
}

// List products with pagination and filtering - enhanced security
async function handleList(supabase: any, profile: any, params: any = {}, ip: string) {
  // Validate and sanitize parameters
  const {
    page = 1,
    limit = 10,
    search = '',
    category = '',
    featured = null,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = params || {}

  // Validate pagination parameters
  const validatedPage = Math.max(1, Math.min(1000, Number(page) || 1))
  const validatedLimit = Math.max(1, Math.min(100, Number(limit) || 10))
  const offset = (validatedPage - 1) * validatedLimit

  // Validate sort parameters
  const allowedSortFields = ['created_at', 'updated_at', 'title', 'featured']
  const validatedSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at'
  const validatedSortOrder = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'desc'

  // Build query
  let query = supabase
    .from('products')
    .select(`
      *,
      categories (
        id,
        name,
        slug
      )
    `, { count: 'exact' })
    .eq('tenant_id', profile.tenant_id)

  // Apply filters
  if (search) {
    query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
  }

  if (category) {
    query = query.eq('category_id', category)
  }

  if (featured !== null) {
    query = query.eq('featured', featured)
  }

  // Apply sorting with validated parameters
  query = query.order(validatedSortBy, { ascending: validatedSortOrder === 'asc' })

  // Apply pagination with validated parameters
  query = query.range(offset, offset + validatedLimit - 1)

  // Execute query with error handling
  const { data: products, error, count } = await query

  if (error) {
    auditLog('LIST_DB_ERROR', profile.id, profile.tenant_id, {
      ip,
      error: error.message,
      params: { page: validatedPage, limit: validatedLimit, search, category }
    })
    throw new Error(`Database error during product listing: ${error.message}`)
  }

  const totalPages = Math.ceil((count || 0) / validatedLimit)

  auditLog('LIST_SUCCESS', profile.id, profile.tenant_id, {
    ip,
    resultCount: products?.length || 0,
    totalCount: count || 0,
    page: validatedPage
  })

  return createSecureResponse({
    success: true,
    products: products || [],
    pagination: {
      page: validatedPage,
      limit: validatedLimit,
      total: count || 0,
      totalPages,
      hasNext: validatedPage < totalPages,
      hasPrev: validatedPage > 1
    }
  })
}
