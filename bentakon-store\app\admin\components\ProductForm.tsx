"use client"

import React, { useState, useCallback } from 'react'
import { Plus, Package, Key, Settings, Save, Trash2, Upload, ChevronDown } from 'lucide-react'
import {
  Product,
  SimplifiedCustomField,
  Category,
  hasPackages,
  createNewPackage,
  createNewCustomField,
  calculateProfitMargin,
  calculateDiscount,
  parseDigitalCodes,
  getDigitalCodesText,
  validateForm,
  prepareFormData
} from '../utils/product-utils'
import type { Dropdown, DropdownOption } from '../../types'

interface ProductFormProps {
  formData: Partial<Product>
  setFormData: React.Dispatch<React.SetStateAction<Partial<Product>>>
  customFields: SimplifiedCustomField[]
  setCustomFields: React.Dispatch<React.SetStateAction<SimplifiedCustomField[]>>
  categories: Category[]
  categoriesLoading: boolean
  onSave: (data: any) => Promise<void>
  onCancel: () => void
  isLoading: boolean
  mode: 'create' | 'edit'
}

// Basic Information Form Component (reused from original)
const ProductBasicForm = ({ formData, setFormData, categories, categoriesLoading }: {
  formData: Partial<Product>
  setFormData: React.Dispatch<React.SetStateAction<Partial<Product>>>
  categories: Category[]
  categoriesLoading: boolean
}) => (
  <div className="space-y-4 md:space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
      <div>
        <label className="block text-sm font-medium mb-2">عنوان المنتج *</label>
        <input
          type="text"
          value={formData.title || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="أدخل عنوان المنتج"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">الفئة *</label>
        {categoriesLoading ? (
          <div className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 text-gray-400">
            جاري التحميل...
          </div>
        ) : (
          <select
            value={formData.category_id || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          >
            <option value="">اختر الفئة</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        )}
      </div>
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">وصف المنتج *</label>
      <textarea
        value={formData.description || ''}
        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        rows={4}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
        placeholder="أدخل وصف مفصل للمنتج"
      />
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">صورة الغلاف *</label>
      <input
        type="url"
        value={formData.cover_image || ''}
        onChange={(e) => setFormData(prev => ({ ...prev, cover_image: e.target.value }))}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
        placeholder="https://example.com/image.jpg"
      />
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">الكلمات المفتاحية</label>
      <input
        type="text"
        value={formData.tags?.join(', ') || ''}
        onChange={(e) => setFormData(prev => ({ 
          ...prev, 
          tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
        }))}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
        placeholder="كلمة1, كلمة2, كلمة3"
      />
    </div>

    <div className="flex items-center space-x-2 space-x-reverse">
      <input
        type="checkbox"
        id="featured"
        checked={formData.featured || false}
        onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
        className="rounded"
      />
      <label htmlFor="featured" className="text-sm text-gray-300">منتج مميز</label>
    </div>
  </div>
)

// Reusable Pricing Input Component
const PricingInputGroup = ({ 
  prices, 
  onChange, 
  showProfitCalculation = false, 
  originalPrice 
}: {
  prices: { original?: number; user?: number; discount?: number; distributor?: number }
  onChange: (field: string, value: number | undefined) => void
  showProfitCalculation?: boolean
  originalPrice?: number
}) => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium mb-2">السعر الأصلي *</label>
        <input
          type="number"
          step="0.01"
          value={prices.original || ''}
          onChange={(e) => onChange('original', Number(e.target.value) || undefined)}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="0.00"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">سعر المستخدم *</label>
        <input
          type="number"
          step="0.01"
          value={prices.user || ''}
          onChange={(e) => onChange('user', Number(e.target.value) || undefined)}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="0.00"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">سعر الخصم</label>
        <input
          type="number"
          step="0.01"
          value={prices.discount || ''}
          onChange={(e) => onChange('discount', Number(e.target.value) || undefined)}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="0.00"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">سعر الموزع</label>
        <input
          type="number"
          step="0.01"
          value={prices.distributor || ''}
          onChange={(e) => onChange('distributor', Number(e.target.value) || undefined)}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="0.00"
        />
      </div>
    </div>

    {/* Profit Calculations */}
    {showProfitCalculation && prices.original && prices.user && (
      <div className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30">
        <h4 className="text-sm font-medium mb-3 text-gray-300">حسابات الربح:</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <p className="text-gray-400 mb-1">ربح المستخدم:</p>
            <p className="font-medium text-green-400">
              {calculateProfitMargin(prices.original, prices.user).toFixed(2)} USD
            </p>
          </div>

          {prices.discount && (
            <div>
              <p className="text-gray-400 mb-1">ربح الخصم:</p>
              <p className="font-medium text-green-400">
                {calculateProfitMargin(prices.original, prices.discount).toFixed(2)} USD
              </p>
            </div>
          )}

          {prices.distributor && (
            <div>
              <p className="text-gray-400 mb-1">ربح الموزع:</p>
              <p className="font-medium text-green-400">
                {calculateProfitMargin(prices.original, prices.distributor).toFixed(2)} USD
              </p>
            </div>
          )}
        </div>
      </div>
    )}
  </div>
)

export default function ProductForm({
  formData,
  setFormData,
  customFields,
  setCustomFields,
  categories,
  categoriesLoading,
  onSave,
  onCancel,
  isLoading,
  mode
}: ProductFormProps) {
  const [activeTab, setActiveTab] = useState<'basic' | 'pricing' | 'packages' | 'fields'>('basic')

  // Smart tab visibility - hide pricing when packages exist
  const showPricingTab = !hasPackages(formData)

  // Package management functions
  const addPackage = useCallback(() => {
    const newPackage = createNewPackage()
    setFormData(prev => ({
      ...prev,
      packages: [...(prev.packages || []), newPackage]
    }))
  }, [setFormData])

  const updatePackage = useCallback((index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) => 
        i === index ? { ...pkg, [field]: value } : pkg
      ) || []
    }))
  }, [setFormData])

  const removePackage = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.filter((_, i) => i !== index) || []
    }))
  }, [setFormData])

  // Digital codes management
  const updatePackageDigitalCodes = useCallback((packageIndex: number, codesText: string) => {
    const codes = parseDigitalCodes(codesText)
    
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) =>
        i === packageIndex
          ? {
              ...pkg,
              digitalCodes: codes,
              hasDigitalCodes: codes.length > 0,
              has_digital_codes: codes.length > 0,
              availableCodesCount: codes.length,
              // Smart inventory management based on digital codes
              track_inventory: codes.length > 0 ? false : true,
              unlimited_stock: codes.length > 0 ? false : true,
              manual_quantity: codes.length > 0 ? 0 : 0,
            }
          : pkg,
      ) || [],
    }))
  }, [setFormData])

  // Custom fields management
  const addCustomField = useCallback(() => {
    const newField = createNewCustomField(customFields)
    setCustomFields(prev => [...prev, newField])
  }, [customFields, setCustomFields])

  const updateCustomField = useCallback((index: number, field: keyof SimplifiedCustomField, value: any) => {
    setCustomFields(prev =>
      prev.map((customField, i) => (i === index ? { ...customField, [field]: value } : customField))
    )
  }, [setCustomFields])

  const removeCustomField = useCallback((index: number) => {
    setCustomFields(prev => prev.filter((_, i) => i !== index))
  }, [setCustomFields])


  // Form submission
  const handleSave = useCallback(async () => {
    const errors = validateForm(formData, customFields)
    
    if (errors.length > 0) {
      alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'))
      return
    }

    const productData = prepareFormData(formData, customFields)
    await onSave(productData)
  }, [formData, customFields, onSave])

  // Tab configuration with smart visibility
  const tabs = [
    { id: 'basic', label: 'المعلومات الأساسية', shortLabel: 'أساسي', icon: Package },
    ...(showPricingTab ? [{ id: 'pricing', label: 'التسعير', shortLabel: 'تسعير', icon: Key }] : []),
    { id: 'packages', label: 'الحزم', shortLabel: 'حزم', icon: Package },
    { id: 'fields', label: 'الحقول المخصصة', shortLabel: 'حقول', icon: Settings }
  ]

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Tabs Navigation */}
      <div className="border-b border-gray-600/50">
        <nav className="flex space-x-2 md:space-x-8 rtl:space-x-reverse overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse py-2 md:py-3 px-2 md:px-3 border-b-2 font-medium text-xs md:text-sm transition-colors whitespace-nowrap min-h-[44px] touch-target ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 flex-shrink-0" />
                <span className="hidden md:inline">{tab.label}</span>
                <span className="md:hidden">{tab.shortLabel}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {/* Basic Info Tab */}
      {activeTab === 'basic' && (
        <ProductBasicForm
          formData={formData}
          setFormData={setFormData}
          categories={categories}
          categoriesLoading={categoriesLoading}
        />
      )}

      {/* Pricing Tab - Only shown when no packages exist */}
      {activeTab === 'pricing' && showPricingTab && (
        <div className="space-y-4 md:space-y-6">
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <Key className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-medium text-blue-400">تسعير المنتج</h3>
            </div>

            <div className="text-sm text-gray-300 mb-4">
              <p className="mb-2">يُستخدم عند عدم وجود حزم للمنتج:</p>
              <ul className="text-xs text-gray-400 space-y-1">
                <li>• السعر الأصلي: تكلفة المنتج الأساسية</li>
                <li>• سعر المستخدم: السعر النهائي للعملاء</li>
                <li>• سعر الخصم: سعر مخفض اختياري</li>
                <li>• سعر الموزع: سعر خاص للموزعين</li>
              </ul>
            </div>

            <PricingInputGroup
              prices={{
                original: formData.original_price,
                user: formData.user_price,
                discount: formData.discount_price,
                distributor: formData.distributor_price
              }}
              onChange={(field, value) => setFormData(prev => ({ ...prev, [`${field}_price`]: value }))}
              showProfitCalculation={true}
            />
          </div>
        </div>
      )}

      {/* Packages Tab */}
      {activeTab === 'packages' && (
        <div className="space-y-4 md:space-y-6">
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">حزم المنتج</h3>
              <button
                type="button"
                onClick={addPackage}
                className="btn-secondary flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                إضافة حزمة
              </button>
            </div>

            {(!formData.packages || formData.packages.length === 0) && (
              <div className="text-center py-8 bg-gray-700/30 rounded-lg border border-gray-600/50">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-400 mb-4">لا توجد حزم للمنتج</p>
                <button
                  type="button"
                  onClick={addPackage}
                  className="btn-primary"
                >
                  إضافة حزمة جديدة
                </button>
              </div>
            )}

            <div className="space-y-6">
              {formData.packages?.map((pkg, index) => (
                <div key={index} className="bg-gray-700/30 rounded-lg p-6 border border-gray-600/50">
                  {/* Package Basic Info */}
                  <div className="space-y-4 mb-4">
                    <input
                      type="text"
                      value={pkg.name}
                      onChange={(e) => updatePackage(index, "name", e.target.value)}
                      placeholder="اسم الحزمة *"
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                    />

                    <textarea
                      value={pkg.description || ""}
                      onChange={(e) => updatePackage(index, "description", e.target.value)}
                      placeholder="وصف الحزمة"
                      rows={3}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
                    />
                  </div>

                  {/* Package Pricing */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-3 text-gray-300">تسعير الحزمة:</h4>
                    <PricingInputGroup
                      prices={{
                        original: pkg.original_price,
                        user: pkg.user_price,
                        discount: pkg.discount_price,
                        distributor: pkg.distributor_price
                      }}
                      onChange={(field, value) => updatePackage(index, `${field}_price`, value)}
                      showProfitCalculation={true}
                    />
                  </div>

                  {/* Package Image Options */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-3 text-gray-300">صورة الحزمة:</h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="radio"
                          id={`use-product-image-${index}`}
                          name={`image-type-${index}`}
                          checked={pkg.use_product_image}
                          onChange={() => {
                            updatePackage(index, "use_product_image", true)
                            updatePackage(index, "image_reference_type", "product_image")
                          }}
                        />
                        <label htmlFor={`use-product-image-${index}`} className="text-sm text-gray-300">
                          استخدام صورة المنتج
                        </label>
                      </div>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="radio"
                          id={`custom-image-${index}`}
                          name={`image-type-${index}`}
                          checked={!pkg.use_product_image}
                          onChange={() => {
                            updatePackage(index, "use_product_image", false)
                            updatePackage(index, "image_reference_type", "url")
                          }}
                        />
                        <label htmlFor={`custom-image-${index}`} className="text-sm text-gray-300">
                          صورة مخصصة
                        </label>
                      </div>

                      {!pkg.use_product_image && (
                        <input
                          type="url"
                          value={pkg.image || ""}
                          onChange={(e) => updatePackage(index, "image", e.target.value)}
                          placeholder="رابط صورة الحزمة"
                          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                        />
                      )}
                    </div>
                  </div>

                  {/* Digital Codes Section */}
                  <div className="mb-6">
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                      <div className="flex items-center space-x-2 space-x-reverse mb-3">
                        <Key className="w-5 h-5 text-blue-400" />
                        <h4 className="text-sm font-medium text-blue-400">الأكواد الرقمية</h4>
                      </div>

                      <p className="text-xs text-gray-400 mb-3">
                        أدخل الأكواد الرقمية (كود واحد في كل سطر). عند إضافة أكواد، سيتم تعطيل إدارة المخزون اليدوي تلقائياً.
                      </p>

                      <textarea
                        value={getDigitalCodesText(pkg)}
                        onChange={(e) => updatePackageDigitalCodes(index, e.target.value)}
                        placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12&#10;9GHT-LMK3-992Z"
                        rows={4}
                        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 font-mono text-sm resize-none"
                      />

                      {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                        <div className="mt-3 text-sm">
                          <span className="text-green-400">✓ {pkg.digitalCodes.length} كود متاح</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Manual Inventory Management - Only show when no digital codes */}
                  {!(pkg.hasDigitalCodes || pkg.has_digital_codes || (pkg.digitalCodes && pkg.digitalCodes.length > 0)) && (
                    <div className="mb-6">
                      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                        <div className="flex items-center space-x-2 space-x-reverse mb-3">
                          <Package className="w-5 h-5 text-green-400" />
                          <h4 className="text-sm font-medium text-green-400">إدارة المخزون</h4>
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <input
                              type="radio"
                              id={`unlimited-${index}`}
                              name={`inventory-${index}`}
                              checked={pkg.unlimited_stock || false}
                              onChange={() => {
                                updatePackage(index, "unlimited_stock", true)
                                updatePackage(index, "manual_quantity", 0)
                              }}
                            />
                            <label htmlFor={`unlimited-${index}`} className="text-sm text-gray-300">
                              مخزون غير محدود
                            </label>
                          </div>

                          <div className="flex items-center space-x-2 space-x-reverse">
                            <input
                              type="radio"
                              id={`limited-${index}`}
                              name={`inventory-${index}`}
                              checked={!pkg.unlimited_stock}
                              onChange={() => {
                                updatePackage(index, "unlimited_stock", false)
                                if ((pkg.manual_quantity || 0) === 0) {
                                  updatePackage(index, "manual_quantity", 100)
                                }
                              }}
                            />
                            <label htmlFor={`limited-${index}`} className="text-sm text-gray-300">
                              كمية محددة
                            </label>
                          </div>

                          {!pkg.unlimited_stock && (
                            <input
                              type="number"
                              value={pkg.manual_quantity || 0}
                              onChange={(e) => updatePackage(index, "manual_quantity", Number(e.target.value) || 0)}
                              placeholder="الكمية المتاحة"
                              min="0"
                              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-500/20"
                            />
                          )}

                          <div className="mt-4 p-3 bg-gray-700/30 rounded-lg border border-gray-600/30">
                            <p className="text-xs font-medium mb-2 text-gray-300">حالة المخزون:</p>
                            {pkg.unlimited_stock ? (
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                <span className="text-xs text-green-400">مخزون غير محدود</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <div className={`w-2 h-2 rounded-full ${(pkg.manual_quantity || 0) > 0 ? 'bg-green-400' : 'bg-red-400'}`}></div>
                                <span className={`text-xs ${(pkg.manual_quantity || 0) > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  {(pkg.manual_quantity || 0) > 0 ? `${pkg.manual_quantity} قطعة متاحة` : 'نفد المخزون'}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Remove Package Button */}
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => removePackage(index)}
                      className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors flex items-center space-x-2 space-x-reverse"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>حذف الحزمة</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Simplified Custom Fields Tab */}
      {activeTab === 'fields' && (
        <div className="space-y-4 md:space-y-6">
          <div>
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium">الحقول المخصصة</h3>
                <p className="text-sm text-gray-400">حقول إضافية يملؤها العميل عند الطلب (نصية أو قوائم منسدلة)</p>
              </div>
              <button
                type="button"
                onClick={addCustomField}
                className="btn-secondary flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Plus className="w-4 h-4" />
                <span>إضافة حقل</span>
              </button>
            </div>

            {customFields.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <Settings className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>لا توجد حقول مخصصة</p>
              </div>
            )}

            <div className="space-y-4">
              {customFields.map((field, index) => (
                <div key={index} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block text-xs font-medium mb-1 text-gray-300">تسمية الحقل *</label>
                      <input
                        type="text"
                        value={field.label}
                        onChange={(e) => updateCustomField(index, 'label', e.target.value)}
                        placeholder="مثال: البريد الإلكتروني"
                        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium mb-1 text-gray-300">نوع الحقل</label>
                      <select
                        value={field.field_type || 'text'}
                        onChange={(e) => updateCustomField(index, 'field_type', e.target.value)}
                        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      >
                        <option value="text">حقل نصي</option>
                        <option value="dropdown">قائمة منسدلة</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium mb-1 text-gray-300">النص التوضيحي</label>
                      <input
                        type="text"
                        value={field.placeholder || ''}
                        onChange={(e) => updateCustomField(index, 'placeholder', e.target.value)}
                        placeholder={field.field_type === 'dropdown' ? "مثال: اختر نوع الخدمة" : "مثال: أدخل بريدك الإلكتروني"}
                        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-xs font-medium mb-1 text-gray-300">وصف الحقل</label>
                    <textarea
                      value={field.description || ''}
                      onChange={(e) => updateCustomField(index, 'description', e.target.value)}
                      placeholder="وصف مفصل لغرض هذا الحقل..."
                      rows={3}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
                    />
                  </div>

                  {/* Dropdown Options - only show for dropdown fields */}
                  {field.field_type === 'dropdown' && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-3">
                        <label className="block text-xs font-medium text-gray-300">خيارات القائمة المنسدلة</label>
                        <button
                          type="button"
                          onClick={() => {
                            const newOption = { label: '', value: '' }
                            const updatedOptions = [...(field.options || []), newOption]
                            updateCustomField(index, 'options', updatedOptions)
                          }}
                          className="text-purple-400 hover:text-purple-300 text-sm flex items-center space-x-1 rtl:space-x-reverse"
                        >
                          <Plus className="w-3 h-3" />
                          <span>إضافة خيار</span>
                        </button>
                      </div>

                      <div className="space-y-2">
                        {(field.options || []).map((option, optionIndex) => (
                          <div key={optionIndex} className="flex items-center space-x-2 rtl:space-x-reverse bg-gray-800/50 rounded-lg p-3">
                            <div className="flex-1 grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                value={option.label}
                                onChange={(e) => {
                                  const updatedOptions = [...(field.options || [])]
                                  updatedOptions[optionIndex] = { ...option, label: e.target.value }
                                  updateCustomField(index, 'options', updatedOptions)
                                }}
                                placeholder="تسمية الخيار"
                                className="bg-gray-700/50 border border-gray-600/50 rounded px-2 py-1 text-sm focus:outline-none focus:border-purple-500"
                              />
                              <input
                                type="text"
                                value={option.value}
                                onChange={(e) => {
                                  const updatedOptions = [...(field.options || [])]
                                  updatedOptions[optionIndex] = { ...option, value: e.target.value }
                                  updateCustomField(index, 'options', updatedOptions)
                                }}
                                placeholder="قيمة الخيار"
                                className="bg-gray-700/50 border border-gray-600/50 rounded px-2 py-1 text-sm focus:outline-none focus:border-purple-500"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() => {
                                const updatedOptions = (field.options || []).filter((_, i) => i !== optionIndex)
                                updateCustomField(index, 'options', updatedOptions)
                              }}
                              className="text-red-400 hover:text-red-300 p-1 rounded hover:bg-red-400/10 transition-colors"
                              title="حذف الخيار"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        ))}

                        {(!field.options || field.options.length === 0) && (
                          <div className="text-center py-4 text-gray-500 text-sm">
                            لا توجد خيارات - اضغط "إضافة خيار" لإضافة خيارات للقائمة المنسدلة
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        checked={field.required}
                        onChange={(e) => updateCustomField(index, 'required', e.target.checked)}
                        className="rounded"
                      />
                      <label className="text-sm text-gray-300">حقل مطلوب</label>
                    </div>

                    <button
                      type="button"
                      onClick={() => removeCustomField(index)}
                      className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors"
                      title="حذف الحقل"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}



      {/* Action Buttons */}
      <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
        <button
          onClick={handleSave}
          disabled={isLoading}
          className="btn-primary flex items-center gap-2"
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
          ) : (
            <Save className="w-4 h-4" />
          )}
          {mode === 'edit' ? 'تحديث المنتج' : 'إضافة المنتج'}
        </button>
        <button
          onClick={onCancel}
          className="btn-secondary"
        >
          إلغاء
        </button>
      </div>
    </div>
  )
}
